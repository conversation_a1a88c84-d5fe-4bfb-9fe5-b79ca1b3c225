"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/common/cms/strapiV5/client.ts":
/*!*******************************************!*\
  !*** ./src/common/cms/strapiV5/client.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCmsAdminPageRelationList: function() { return /* binding */ getCmsAdminPageRelationList; },\n/* harmony export */   getCmsAdminPageRelationListByQuery: function() { return /* binding */ getCmsAdminPageRelationListByQuery; },\n/* harmony export */   getCmsDataAdminClient: function() { return /* binding */ getCmsDataAdminClient; },\n/* harmony export */   publishCmsAdminPageDocument: function() { return /* binding */ publishCmsAdminPageDocument; },\n/* harmony export */   putCmsAdminPageDocument: function() { return /* binding */ putCmsAdminPageDocument; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _barrel_optimize_names_cacheWrapper_fetcher_collective_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=cacheWrapper,fetcher!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/utils/fetch-cache-client.js\");\n/* harmony import */ var _barrel_optimize_names_cacheWrapper_fetcher_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=cacheWrapper,fetcher!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/common/utils.js\");\n\n\n\nvar getCmsDataAdminClient = function() {\n    var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_0__._)(function(param) {\n        var path, _param_deep, deep, _param_locale, locale, _param_draft, draft, filter, ttl, url, options;\n        return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_1__.__generator)(this, function(_state) {\n            path = param.path, _param_deep = param.deep, deep = _param_deep === void 0 ? 4 : _param_deep, _param_locale = param.locale, locale = _param_locale === void 0 ? \"en\" : _param_locale, _param_draft = param.draft, draft = _param_draft === void 0 ? false : _param_draft, filter = param.filter, ttl = param.ttl;\n            if (false) {}\n            url = \"\".concat(\"https://ai-digital-brand-cms-smooth.gocollectives.com\", \"/\").concat(path, \"?pLevel=\").concat(deep, \"&locale=\").concat(locale).concat(draft ? \"&publicationState=preview\" : \"\").concat(filter ? \"&\".concat(filter) : \"\");\n            options = {\n                method: \"GET\",\n                headers: {\n                    authorization: \"Bearer \".concat(localStorage.getItem(\"adminJwt\"))\n                }\n            };\n            return [\n                2,\n                (0,_barrel_optimize_names_cacheWrapper_fetcher_collective_core__WEBPACK_IMPORTED_MODULE_2__.cacheWrapper)(url, options, ttl)\n            ];\n        });\n    });\n    return function getCmsDataAdminClient(_) {\n        return _ref.apply(this, arguments);\n    };\n}();\nvar getCmsAdminPageRelationListByQuery = function() {\n    var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_0__._)(function(param) {\n        var uid, fieldName, documentId, _param_pageSize, pageSize, _param_locale, locale, filter, url, options;\n        return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_1__.__generator)(this, function(_state) {\n            uid = param.uid, fieldName = param.fieldName, documentId = param.documentId, _param_pageSize = param.pageSize, pageSize = _param_pageSize === void 0 ? 50 : _param_pageSize, _param_locale = param.locale, locale = _param_locale === void 0 ? \"en\" : _param_locale, filter = param.filter;\n            if (false) {}\n            url = \"\".concat(\"https://ai-digital-brand-cms-smooth.gocollectives.com\", \"/content-manager/relations/\").concat(uid, \"/\").concat(fieldName, \"?id=\").concat(documentId, \"&pageSize=\").concat(pageSize, \"&locale=\").concat(locale).concat(filter ? \"&\".concat(filter) : \"\");\n            options = {\n                method: \"GET\",\n                headers: {\n                    authorization: \"Bearer \".concat(localStorage.getItem(\"adminJwt\"))\n                }\n            };\n            return [\n                2,\n                (0,_barrel_optimize_names_cacheWrapper_fetcher_collective_core__WEBPACK_IMPORTED_MODULE_2__.cacheWrapper)(url, options)\n            ];\n        });\n    });\n    return function getCmsAdminPageRelationListByQuery(_) {\n        return _ref.apply(this, arguments);\n    };\n}();\nvar getCmsAdminPageRelationList = function() {\n    var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_0__._)(function(param) {\n        var uid, documentId, fieldName, _param_pageSize, pageSize, _param_locale, locale, filter, url, options;\n        return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_1__.__generator)(this, function(_state) {\n            uid = param.uid, documentId = param.documentId, fieldName = param.fieldName, _param_pageSize = param.pageSize, pageSize = _param_pageSize === void 0 ? 5 : _param_pageSize, _param_locale = param.locale, locale = _param_locale === void 0 ? \"en\" : _param_locale, filter = param.filter;\n            if (false) {}\n            url = \"\".concat(\"https://ai-digital-brand-cms-smooth.gocollectives.com\", \"/content-manager/relations/\").concat(uid, \"/\").concat(documentId, \"/\").concat(fieldName, \"?pageSize=\").concat(pageSize, \"&locale=\").concat(locale).concat(filter ? \"&\".concat(filter) : \"\");\n            options = {\n                method: \"GET\",\n                headers: {\n                    authorization: \"Bearer \".concat(localStorage.getItem(\"adminJwt\"))\n                }\n            };\n            return [\n                2,\n                (0,_barrel_optimize_names_cacheWrapper_fetcher_collective_core__WEBPACK_IMPORTED_MODULE_2__.cacheWrapper)(url, options, 15)\n            ];\n        });\n    });\n    return function getCmsAdminPageRelationList(_) {\n        return _ref.apply(this, arguments);\n    };\n}();\nvar putCmsAdminPageDocument = function() {\n    var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_0__._)(function(param) {\n        var kind0, uid, documentId, data, kind, url, options;\n        return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_1__.__generator)(this, function(_state) {\n            kind0 = param.kind, uid = param.uid, documentId = param.documentId, data = param.data;\n            if (false) {}\n            kind = kind0 === \"collectionType\" ? \"collection-types\" : \"single-types\";\n            url = \"\".concat(\"https://ai-digital-brand-cms-smooth.gocollectives.com\", \"/content-manager/\").concat(kind, \"/api::\").concat(uid, \".\").concat(uid, \"/\").concat(documentId);\n            options = {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    authorization: \"Bearer \".concat(localStorage.getItem(\"adminJwt\"))\n                },\n                body: JSON.stringify(data)\n            };\n            return [\n                2,\n                (0,_barrel_optimize_names_cacheWrapper_fetcher_collective_core__WEBPACK_IMPORTED_MODULE_3__.fetcher)(url, options)\n            ];\n        });\n    });\n    return function putCmsAdminPageDocument(_) {\n        return _ref.apply(this, arguments);\n    };\n}();\nvar publishCmsAdminPageDocument = function() {\n    var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_0__._)(function(param) {\n        var kind0, uid, documentId, data, kind, url, options;\n        return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_1__.__generator)(this, function(_state) {\n            kind0 = param.kind, uid = param.uid, documentId = param.documentId, data = param.data;\n            if (false) {}\n            kind = kind0 === \"collectionType\" ? \"collection-types\" : \"single-types\";\n            url = \"\".concat(\"https://ai-digital-brand-cms-smooth.gocollectives.com\", \"/content-manager/\").concat(kind, \"/api::\").concat(uid, \".\").concat(uid, \"/\").concat(kind0 === \"collectionType\" ? documentId + \"/\" : \"\", \"actions/publish\");\n            options = {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    authorization: \"Bearer \".concat(localStorage.getItem(\"adminJwt\"))\n                },\n                body: JSON.stringify(data)\n            };\n            return [\n                2,\n                (0,_barrel_optimize_names_cacheWrapper_fetcher_collective_core__WEBPACK_IMPORTED_MODULE_3__.fetcher)(url, options)\n            ];\n        });\n    });\n    return function publishCmsAdminPageDocument(_) {\n        return _ref.apply(this, arguments);\n    };\n}();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/common/cms/strapiV5/client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/layouts/builder/content/CollectionTypeLayout.tsx":
/*!**************************************************************!*\
  !*** ./src/layouts/builder/content/CollectionTypeLayout.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CollectionTypeLayout: function() { return /* binding */ CollectionTypeLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Input!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Input/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Input!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../../node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_DataTable__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/DataTable */ \"(app-pages-browser)/./src/components/DataTable/DataTable.tsx\");\n/* harmony import */ var _components_DataTable__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/DataTable */ \"(app-pages-browser)/./src/components/DataTable/Column.tsx\");\n/* harmony import */ var _components_Paginator__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/Paginator */ \"(app-pages-browser)/./src/components/Paginator/Paginator.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _mock_Navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/mock/Navigation */ \"(app-pages-browser)/./src/mock/Navigation.ts\");\n/* harmony import */ var common_cms__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! common/cms */ \"(app-pages-browser)/./src/common/cms/strapiV5/client.ts\");\n/* harmony import */ var _contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./contentbuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/content/contentbuilderlayout.module.scss\");\n/* harmony import */ var _contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _SingleTypeLayout__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SingleTypeLayout */ \"(app-pages-browser)/./src/layouts/builder/content/SingleTypeLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ CollectionTypeLayout auto */ \n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nvar CollectionTypeLayout = function(param) {\n    var lng = param.lng, uid = param.uid;\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var pageSlug = context.slug;\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]), 2), cmsData = _useState[0], setCmsData = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(5), 2), pageSize = _useState1[0], setPageSize = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1), 2), currentPage = _useState2[0], setCurrentPage = _useState2[1];\n    var currentNav = _mock_Navigation__WEBPACK_IMPORTED_MODULE_6__.NavigationData.find(function(nav) {\n        return nav.apiId === pageSlug[0];\n    });\n    var currentType = currentNav === null || currentNav === void 0 ? void 0 : currentNav.layouts.find(function(layout) {\n        return layout.apiId === pageSlug[1];\n    });\n    var isEditMode = pageSlug.length >= 4 && pageSlug[pageSlug.length - 1] === \"edit\";\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(-1), 2), curCmsDataIndex = _useState3[0], setCurCmsDataIndex = _useState3[1];\n    (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(function() {\n        var fetchData = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_8__._)(function() {\n                var res;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_9__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            return [\n                                4,\n                                (0,common_cms__WEBPACK_IMPORTED_MODULE_10__.getCmsDataAdminClient)({\n                                    path: \"content-manager/collection-types/api::\".concat(uid, \".\").concat(uid),\n                                    deep: 3,\n                                    locale: lng,\n                                    filter: \"pageSize=1000\"\n                                })\n                            ];\n                        case 1:\n                            res = _state.sent();\n                            setCmsData(res.results);\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function fetchData() {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        fetchData();\n    }, [\n        uid,\n        lng\n    ]);\n    (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(function() {\n        if (isEditMode === false) return;\n        if (!cmsData || cmsData.length < 1) return;\n        var index = cmsData.findIndex(function(data) {\n            return data.slug === pageSlug[pageSlug.length - 2];\n        });\n        if (index === -1) {\n            router.push(pathname.split(\"/\").slice(0, 2).join(\"/\"));\n        }\n        setCurCmsDataIndex(index);\n    }, [\n        cmsData,\n        isEditMode\n    ]);\n    if (!currentType) {\n        return null;\n    }\n    if (isEditMode && curCmsDataIndex !== -1) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SingleTypeLayout__WEBPACK_IMPORTED_MODULE_11__.SingleTypeLayout, {\n            lng: lng,\n            kind: \"collectionType\",\n            uid: uid,\n            initData: cmsData[curCmsDataIndex]\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n            lineNumber: 59,\n            columnNumber: 4\n        }, _this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().collection__type),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().heading),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"collect__heading\",\n                                children: \"Database\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"search\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 18\n                                }, void 0),\n                                className: (_contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().input),\n                                placeholder: \"Search..\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DataTable__WEBPACK_IMPORTED_MODULE_14__.DataTable, {\n                        tableStyle: {\n                            minWidth: \"50em\"\n                        },\n                        value: cmsData.slice((currentPage - 1) * pageSize, currentPage * pageSize).map(function(item) {\n                            if (item[currentType.identifierField] !== null) {\n                                if (currentType.defaultMode === \"builder\") {\n                                    item.rowLink = \"/content-builder/\".concat(pathname.split(\"/\").slice(2).join(\"/\"), \"/\").concat(item[currentType.identifierField]);\n                                } else {\n                                    item.rowLink = \"/content-manager/\".concat(pathname.split(\"/\").slice(2).join(\"/\"), \"/\").concat(item[currentType.identifierField], \"/edit\");\n                                }\n                            // item.rowLink = `/content-builder/${pathname.split('/').slice(2).join('/')}/${item[currentType.identifierField]}`\n                            }\n                            return item;\n                        }),\n                        selectionMode: \"checkbox\",\n                        onSelectionChange: function(e) {\n                            return console.log(e);\n                        },\n                        dataKey: \"documentId\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DataTable__WEBPACK_IMPORTED_MODULE_15__.Column, {\n                                selectionMode: \"multiple\",\n                                headerStyle: {\n                                    width: \"3rem\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DataTable__WEBPACK_IMPORTED_MODULE_15__.Column, {\n                                field: \"id\",\n                                header: \"ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DataTable__WEBPACK_IMPORTED_MODULE_15__.Column, {\n                                field: \"Headline\",\n                                header: \"Headline\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DataTable__WEBPACK_IMPORTED_MODULE_15__.Column, {\n                                field: \"slug\",\n                                header: \"Slug\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DataTable__WEBPACK_IMPORTED_MODULE_15__.Column, {\n                                field: \"createdDate\",\n                                header: \"Created Date\",\n                                body: function(row) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: row.createdAt\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 40\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                lineNumber: 70,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Paginator__WEBPACK_IMPORTED_MODULE_16__.Paginator, {\n                totalCount: cmsData.length,\n                onPageChange: function(num) {\n                    return setCurrentPage(num);\n                },\n                onPageSizeChange: function(num) {\n                    return setPageSize(num);\n                },\n                pageSize: pageSize,\n                currentPage: currentPage\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\CollectionTypeLayout.tsx\",\n                lineNumber: 108,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true);\n};\n_s(CollectionTypeLayout, \"7gwQvNjKOrsbZwH+0LuQStMy0uw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        framer_motion__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect,\n        framer_motion__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect\n    ];\n});\n_c = CollectionTypeLayout;\nvar _c;\n$RefreshReg$(_c, \"CollectionTypeLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/content/CollectionTypeLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/layouts/builder/content/ContentBuilderLayout.tsx":
/*!**************************************************************!*\
  !*** ./src/layouts/builder/content/ContentBuilderLayout.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentBuilderlayout: function() { return /* binding */ ContentBuilderlayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _CollectionTypeLayout__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CollectionTypeLayout */ \"(app-pages-browser)/./src/layouts/builder/content/CollectionTypeLayout.tsx\");\n/* harmony import */ var _contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contentbuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/content/contentbuilderlayout.module.scss\");\n/* harmony import */ var _contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _SingleTypeLayout__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./SingleTypeLayout */ \"(app-pages-browser)/./src/layouts/builder/content/SingleTypeLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ ContentBuilderlayout auto */ \nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar ContentBuilderlayout = function(param) {\n    var lng = param.lng, value = param.value, data = param.data, _param_activeTabIndex = param.activeTabIndex, activeTabIndex = _param_activeTabIndex === void 0 ? 0 : _param_activeTabIndex;\n    _s();\n    var slug = value.slug;\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(data[activeTabIndex === -1 ? 0 : activeTabIndex]), 2), currentTab = _useState[0], setCurrentTab = _useState[1];\n    var handleTabChange = function(tab) {\n        if (slug.length >= 4 && slug[slug.length - 1] === \"edit\" && currentTab) {\n            router.push(\"\".concat(pathname.split(\"/\").slice(0, 3).join(\"/\"), \"/\").concat(currentTab.apiId));\n        }\n        setCurrentTab(tab);\n    };\n    // Set link to correct apiId of current tab\n    (0,_barrel_optimize_names_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect)(function() {\n        if (!currentTab) {\n            return;\n        }\n        var newPath = pathname;\n        if (slug.length >= 3 && slug[1] === currentTab.apiId && slug[slug.length - 1] === \"edit\") {\n            return;\n        }\n        if (currentTab.defaultMode === \"builder\" && currentTab.kind === \"singleType\") {\n            newPath = newPath.replace(\"content-manager\", \"content-builder\");\n            router.push(\"\".concat(newPath.split(\"/\").slice(0, 3).join(\"/\"), \"/\").concat(currentTab.apiId));\n        } else {\n            router.push(\"\".concat(newPath.split(\"/\").slice(0, 3).join(\"/\"), \"/\").concat(currentTab.apiId));\n        }\n    }, [\n        pathname,\n        currentTab\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_7__.PageBuilderProvider, {\n        value: value,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().wrapper)),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().navigation),\n                    children: data.map(function(item, index) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((currentTab === null || currentTab === void 0 ? void 0 : currentTab.uid) === item.uid && (_contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().active), \"collect__button--lg\"),\n                            onClick: function() {\n                                return handleTabChange(item);\n                            },\n                            children: item.kind === \"singleType\" ? \"Edit \".concat(item.name) : \"Database \".concat(item.name)\n                        }, index, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\ContentBuilderLayout.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 8\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\ContentBuilderLayout.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 5\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_contentbuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().content),\n                    children: currentTab && currentTab.kind === \"collectionType\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CollectionTypeLayout__WEBPACK_IMPORTED_MODULE_8__.CollectionTypeLayout, {\n                        lng: lng,\n                        uid: currentTab.uid\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\ContentBuilderLayout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 7\n                    }, _this) : currentTab && currentTab.kind === \"singleType\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SingleTypeLayout__WEBPACK_IMPORTED_MODULE_9__.SingleTypeLayout, {\n                        lng: lng,\n                        kind: \"singleType\",\n                        uid: currentTab.uid\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\ContentBuilderLayout.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 7\n                    }, _this) : null\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\ContentBuilderLayout.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 5\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\ContentBuilderLayout.tsx\",\n            lineNumber: 56,\n            columnNumber: 4\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\ContentBuilderLayout.tsx\",\n        lineNumber: 55,\n        columnNumber: 3\n    }, _this);\n};\n_s(ContentBuilderlayout, \"KvP7lyEQy5reEeSjUN0FKB+ScL0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _barrel_optimize_names_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect\n    ];\n});\n_c = ContentBuilderlayout;\nvar _c;\n$RefreshReg$(_c, \"ContentBuilderlayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9sYXlvdXRzL2J1aWxkZXIvY29udGVudC9Db250ZW50QnVpbGRlckxheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRzREO0FBQ2pDO0FBQzZCO0FBQ3hCO0FBQ3NEO0FBRXpCO0FBQ047QUFDRjtBQUU5QyxJQUFNUyx1QkFBdUI7UUFDbkNDLFlBQUFBLEtBQ0FDLGNBQUFBLE9BQ0FDLGFBQUFBLG9DQUNBQyxnQkFBQUEsb0RBQWlCOztJQU9qQixJQUFNLE9BQVdGLE1BQVRHO0lBQ1IsSUFBTUMsU0FBU2IsMERBQVNBO0lBQ3hCLElBQU1jLFdBQVdiLDREQUFXQTtJQUM1QixJQUFvQ0MsWUFBQUEsK0RBQUFBLENBQUFBLCtDQUFRQSxDQUFDUSxJQUFJLENBQUNDLG1CQUFtQixDQUFDLElBQUksSUFBSUEsZUFBZSxPQUF0RkksYUFBNkJiLGNBQWpCYyxnQkFBaUJkO0lBRXBDLElBQU1lLGtCQUFrQixTQUFDQztRQUN4QixJQUFJTixLQUFLTyxNQUFNLElBQUksS0FBS1AsSUFBSSxDQUFDQSxLQUFLTyxNQUFNLEdBQUcsRUFBRSxLQUFLLFVBQVVKLFlBQVk7WUFDdkVGLE9BQU9PLElBQUksQ0FBQyxHQUFnREwsT0FBN0NELFNBQVNPLEtBQUssQ0FBQyxLQUFLQyxLQUFLLENBQUMsR0FBRyxHQUFHQyxJQUFJLENBQUMsTUFBSyxLQUFvQixPQUFqQlIsV0FBV1MsS0FBSztRQUM3RTtRQUNBUixjQUFjRTtJQUNmO0lBRUEsMkNBQTJDO0lBQzNDcEIsMkhBQXlCQSxDQUFDO1FBQ3pCLElBQUksQ0FBQ2lCLFlBQVk7WUFDaEI7UUFDRDtRQUNBLElBQUlVLFVBQVVYO1FBQ2QsSUFBSUYsS0FBS08sTUFBTSxJQUFJLEtBQUtQLElBQUksQ0FBQyxFQUFFLEtBQUtHLFdBQVdTLEtBQUssSUFBSVosSUFBSSxDQUFDQSxLQUFLTyxNQUFNLEdBQUcsRUFBRSxLQUFLLFFBQVE7WUFDekY7UUFDRDtRQUNBLElBQUlKLFdBQVdXLFdBQVcsS0FBSyxhQUFhWCxXQUFXWSxJQUFJLEtBQUssY0FBYztZQUM3RUYsVUFBVUEsUUFBUUcsT0FBTyxDQUFDLG1CQUFtQjtZQUM3Q2YsT0FBT08sSUFBSSxDQUFDLEdBQStDTCxPQUE1Q1UsUUFBUUosS0FBSyxDQUFDLEtBQUtDLEtBQUssQ0FBQyxHQUFHLEdBQUdDLElBQUksQ0FBQyxNQUFLLEtBQW9CLE9BQWpCUixXQUFXUyxLQUFLO1FBQzVFLE9BQU87WUFDTlgsT0FBT08sSUFBSSxDQUFDLEdBQStDTCxPQUE1Q1UsUUFBUUosS0FBSyxDQUFDLEtBQUtDLEtBQUssQ0FBQyxHQUFHLEdBQUdDLElBQUksQ0FBQyxNQUFLLEtBQW9CLE9BQWpCUixXQUFXUyxLQUFLO1FBQzVFO0lBQ0QsR0FBRztRQUFDVjtRQUFVQztLQUFXO0lBRXpCLHFCQUNDLDhEQUFDWix5RUFBbUJBO1FBQUNNLE9BQU9BO2tCQUMzQiw0RUFBQ29CO1lBQUlDLFdBQVcvQixpREFBRUEsQ0FBQ00sa0ZBQWM7OzhCQUNoQyw4REFBQ3dCO29CQUFJQyxXQUFXekIscUZBQWlCOzhCQUMvQkssS0FBS3VCLEdBQUcsQ0FBQyxTQUFDQyxNQUFNQzt3QkFDaEIscUJBQ0MsOERBQUNDOzRCQUVBTixXQUFXL0IsaURBQUVBLENBQUNnQixDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVlzQixHQUFHLE1BQUtILEtBQUtHLEdBQUcsSUFBSWhDLGlGQUFhLEVBQUU7NEJBQzdEa0MsU0FBUzt1Q0FBTXRCLGdCQUFnQmlCOztzQ0FFOUJBLEtBQUtQLElBQUksS0FBSyxlQUFlLFFBQWtCLE9BQVZPLEtBQUtNLElBQUksSUFBSyxZQUFzQixPQUFWTixLQUFLTSxJQUFJOzJCQUpwRUw7Ozs7O29CQU9SOzs7Ozs7OEJBRUQsOERBQUNOO29CQUFJQyxXQUFXekIsa0ZBQWM7OEJBQzVCVSxjQUFjQSxXQUFXWSxJQUFJLEtBQUssaUNBQ2xDLDhEQUFDdkIsdUVBQW9CQTt3QkFBQ0ksS0FBS0E7d0JBQUs2QixLQUFLdEIsV0FBV3NCLEdBQUc7Ozs7O2dDQUNoRHRCLGNBQWNBLFdBQVdZLElBQUksS0FBSyw2QkFDckMsOERBQUNyQiwrREFBZ0JBO3dCQUFDRSxLQUFLQTt3QkFBS21CLE1BQUs7d0JBQWFVLEtBQUt0QixXQUFXc0IsR0FBRzs7Ozs7Z0NBQzlEOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtULEVBQUM7R0FsRVk5Qjs7UUFZR1Asc0RBQVNBO1FBQ1BDLHdEQUFXQTtRQVc1QkgsdUhBQXlCQTs7O0tBeEJiUyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbGF5b3V0cy9idWlsZGVyL2NvbnRlbnQvQ29udGVudEJ1aWxkZXJMYXlvdXQudHN4P2I3YjQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBwYXRoIGZyb20gJ3BhdGgnXG5pbXBvcnQgeyB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0IH0gZnJvbSAnQGNvbGxlY3RpdmUvY29yZSdcbmltcG9ydCBjbiBmcm9tICdjbGFzc25hbWVzJ1xuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBQYWdlQnVpbGRlclByb3ZpZGVyLCB0eXBlIEJ1aWxkZXJCYXNlUHJvcHMgfSBmcm9tICdAL2NvbnRleHRzL0J1aWxkZXJDb250ZXh0J1xuaW1wb3J0IHR5cGUgeyBJTmF2aWdhdGlvbktpbmQsIElOYXZpZ2F0aW9uTGF5b3V0UHJvcHMgfSBmcm9tICdAL2NvbnRleHRzL05hdmlnYXRpb25Db250ZXh0J1xuaW1wb3J0IHsgQ29sbGVjdGlvblR5cGVMYXlvdXQgfSBmcm9tICcuL0NvbGxlY3Rpb25UeXBlTGF5b3V0J1xuaW1wb3J0IHN0eWxlcyBmcm9tICcuL2NvbnRlbnRidWlsZGVybGF5b3V0Lm1vZHVsZS5zY3NzJ1xuaW1wb3J0IHsgU2luZ2xlVHlwZUxheW91dCB9IGZyb20gJy4vU2luZ2xlVHlwZUxheW91dCdcblxuZXhwb3J0IGNvbnN0IENvbnRlbnRCdWlsZGVybGF5b3V0ID0gKHtcblx0bG5nLFxuXHR2YWx1ZSxcblx0ZGF0YSxcblx0YWN0aXZlVGFiSW5kZXggPSAwLFxufToge1xuXHRsbmc6IHN0cmluZ1xuXHR2YWx1ZTogQnVpbGRlckJhc2VQcm9wc1xuXHRkYXRhOiBJTmF2aWdhdGlvbkxheW91dFByb3BzW11cblx0YWN0aXZlVGFiSW5kZXg6IG51bWJlclxufSkgPT4ge1xuXHRjb25zdCB7IHNsdWcgfSA9IHZhbHVlXG5cdGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG5cdGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxuXHRjb25zdCBbY3VycmVudFRhYiwgc2V0Q3VycmVudFRhYl0gPSB1c2VTdGF0ZShkYXRhW2FjdGl2ZVRhYkluZGV4ID09PSAtMSA/IDAgOiBhY3RpdmVUYWJJbmRleF0pXG5cblx0Y29uc3QgaGFuZGxlVGFiQ2hhbmdlID0gKHRhYjogKHR5cGVvZiBkYXRhKVswXSkgPT4ge1xuXHRcdGlmIChzbHVnLmxlbmd0aCA+PSA0ICYmIHNsdWdbc2x1Zy5sZW5ndGggLSAxXSA9PT0gJ2VkaXQnICYmIGN1cnJlbnRUYWIpIHtcblx0XHRcdHJvdXRlci5wdXNoKGAke3BhdGhuYW1lLnNwbGl0KCcvJykuc2xpY2UoMCwgMykuam9pbignLycpfS8ke2N1cnJlbnRUYWIuYXBpSWR9YClcblx0XHR9XG5cdFx0c2V0Q3VycmVudFRhYih0YWIpXG5cdH1cblxuXHQvLyBTZXQgbGluayB0byBjb3JyZWN0IGFwaUlkIG9mIGN1cnJlbnQgdGFiXG5cdHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCkgPT4ge1xuXHRcdGlmICghY3VycmVudFRhYikge1xuXHRcdFx0cmV0dXJuXG5cdFx0fVxuXHRcdGxldCBuZXdQYXRoID0gcGF0aG5hbWVcblx0XHRpZiAoc2x1Zy5sZW5ndGggPj0gMyAmJiBzbHVnWzFdID09PSBjdXJyZW50VGFiLmFwaUlkICYmIHNsdWdbc2x1Zy5sZW5ndGggLSAxXSA9PT0gJ2VkaXQnKSB7XG5cdFx0XHRyZXR1cm5cblx0XHR9XG5cdFx0aWYgKGN1cnJlbnRUYWIuZGVmYXVsdE1vZGUgPT09ICdidWlsZGVyJyAmJiBjdXJyZW50VGFiLmtpbmQgPT09ICdzaW5nbGVUeXBlJykge1xuXHRcdFx0bmV3UGF0aCA9IG5ld1BhdGgucmVwbGFjZSgnY29udGVudC1tYW5hZ2VyJywgJ2NvbnRlbnQtYnVpbGRlcicpXG5cdFx0XHRyb3V0ZXIucHVzaChgJHtuZXdQYXRoLnNwbGl0KCcvJykuc2xpY2UoMCwgMykuam9pbignLycpfS8ke2N1cnJlbnRUYWIuYXBpSWR9YClcblx0XHR9IGVsc2Uge1xuXHRcdFx0cm91dGVyLnB1c2goYCR7bmV3UGF0aC5zcGxpdCgnLycpLnNsaWNlKDAsIDMpLmpvaW4oJy8nKX0vJHtjdXJyZW50VGFiLmFwaUlkfWApXG5cdFx0fVxuXHR9LCBbcGF0aG5hbWUsIGN1cnJlbnRUYWJdKVxuXG5cdHJldHVybiAoXG5cdFx0PFBhZ2VCdWlsZGVyUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cblx0XHRcdDxkaXYgY2xhc3NOYW1lPXtjbihzdHlsZXMud3JhcHBlcil9PlxuXHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm5hdmlnYXRpb259PlxuXHRcdFx0XHRcdHtkYXRhLm1hcCgoaXRlbSwgaW5kZXgpID0+IHtcblx0XHRcdFx0XHRcdHJldHVybiAoXG5cdFx0XHRcdFx0XHRcdDxidXR0b25cblx0XHRcdFx0XHRcdFx0XHRrZXk9e2luZGV4fVxuXHRcdFx0XHRcdFx0XHRcdGNsYXNzTmFtZT17Y24oY3VycmVudFRhYj8udWlkID09PSBpdGVtLnVpZCAmJiBzdHlsZXMuYWN0aXZlLCAnY29sbGVjdF9fYnV0dG9uLS1sZycpfVxuXHRcdFx0XHRcdFx0XHRcdG9uQ2xpY2s9eygpID0+IGhhbmRsZVRhYkNoYW5nZShpdGVtKX1cblx0XHRcdFx0XHRcdFx0PlxuXHRcdFx0XHRcdFx0XHRcdHtpdGVtLmtpbmQgPT09ICdzaW5nbGVUeXBlJyA/IGBFZGl0ICR7aXRlbS5uYW1lfWAgOiBgRGF0YWJhc2UgJHtpdGVtLm5hbWV9YH1cblx0XHRcdFx0XHRcdFx0PC9idXR0b24+XG5cdFx0XHRcdFx0XHQpXG5cdFx0XHRcdFx0fSl9XG5cdFx0XHRcdDwvZGl2PlxuXHRcdFx0XHQ8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmNvbnRlbnR9PlxuXHRcdFx0XHRcdHtjdXJyZW50VGFiICYmIGN1cnJlbnRUYWIua2luZCA9PT0gJ2NvbGxlY3Rpb25UeXBlJyA/IChcblx0XHRcdFx0XHRcdDxDb2xsZWN0aW9uVHlwZUxheW91dCBsbmc9e2xuZ30gdWlkPXtjdXJyZW50VGFiLnVpZH0gLz5cblx0XHRcdFx0XHQpIDogY3VycmVudFRhYiAmJiBjdXJyZW50VGFiLmtpbmQgPT09ICdzaW5nbGVUeXBlJyA/IChcblx0XHRcdFx0XHRcdDxTaW5nbGVUeXBlTGF5b3V0IGxuZz17bG5nfSBraW5kPVwic2luZ2xlVHlwZVwiIHVpZD17Y3VycmVudFRhYi51aWR9IC8+XG5cdFx0XHRcdFx0KSA6IG51bGx9XG5cdFx0XHRcdDwvZGl2PlxuXHRcdFx0PC9kaXY+XG5cdFx0PC9QYWdlQnVpbGRlclByb3ZpZGVyPlxuXHQpXG59XG4iXSwibmFtZXMiOlsidXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCIsImNuIiwidXNlUm91dGVyIiwidXNlUGF0aG5hbWUiLCJ1c2VTdGF0ZSIsIlBhZ2VCdWlsZGVyUHJvdmlkZXIiLCJDb2xsZWN0aW9uVHlwZUxheW91dCIsInN0eWxlcyIsIlNpbmdsZVR5cGVMYXlvdXQiLCJDb250ZW50QnVpbGRlcmxheW91dCIsImxuZyIsInZhbHVlIiwiZGF0YSIsImFjdGl2ZVRhYkluZGV4Iiwic2x1ZyIsInJvdXRlciIsInBhdGhuYW1lIiwiY3VycmVudFRhYiIsInNldEN1cnJlbnRUYWIiLCJoYW5kbGVUYWJDaGFuZ2UiLCJ0YWIiLCJsZW5ndGgiLCJwdXNoIiwic3BsaXQiLCJzbGljZSIsImpvaW4iLCJhcGlJZCIsIm5ld1BhdGgiLCJkZWZhdWx0TW9kZSIsImtpbmQiLCJyZXBsYWNlIiwiZGl2IiwiY2xhc3NOYW1lIiwid3JhcHBlciIsIm5hdmlnYXRpb24iLCJtYXAiLCJpdGVtIiwiaW5kZXgiLCJidXR0b24iLCJ1aWQiLCJhY3RpdmUiLCJvbkNsaWNrIiwibmFtZSIsImNvbnRlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/content/ContentBuilderLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/layouts/builder/content/SingleTypeLayout.tsx":
/*!**********************************************************!*\
  !*** ./src/layouts/builder/content/SingleTypeLayout.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SingleTypeLayout: function() { return /* binding */ SingleTypeLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_object_without_properties */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_without_properties.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var common_cms__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! common/cms */ \"(app-pages-browser)/./src/common/cms/strapiV5/client.ts\");\n/* harmony import */ var _singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./singletypelayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/content/singletypelayout.module.scss\");\n/* harmony import */ var _singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ SingleTypeLayout auto */ \n\n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar SingleTypeLayout = function(param) {\n    var lng = param.lng, kind = param.kind, uid = param.uid, initData = param.initData;\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__.PageBuilderContext);\n    var components = context.components, globals = context.globals, contentType = context.contentType, data = context.data, setData = context.setData;\n    var _ref = data !== null && data !== void 0 ? data : {}, cmsData = _ref.data;\n    var fieldSizes = globals.data.fieldSizes;\n    var contentTypes = contentType.data.schema.attributes;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), 2), isInit = _useState[0], setIsInit = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), 2), isEdit = _useState1[0], setIsEdit = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), 2), isRefresh = _useState2[0], setIsRefresh = _useState2[1];\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    var isDraftEnable = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function() {\n        return contentType.data.schema.draftAndPublish;\n    }, [\n        contentType.data.schema\n    ]);\n    // Filtered component's attributes\n    var filteredComponents = function(obj) {\n        return Object.entries(obj || {}).filter(function(param) {\n            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), value = _param[1];\n            return typeof value === \"object\" && value !== null;\n        });\n    };\n    // Filtered cms data's attributes\n    var filteredInfo = function(obj) {\n        return Object.entries(obj || {})// Sort key by alphabetically\n        .sort(function(a, b) {\n            return a[0].localeCompare(b[0]);\n        }).filter(function(param) {\n            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 1), key = _param[0];\n            return ![\n                \"components\",\n                \"id\"\n            ].includes(key);\n        });\n    };\n    var updatedTimeConvert = function(datetime) {\n        var seconds = Math.floor((Date.now() - new Date(datetime).getTime()) / 1000);\n        var intervals = [\n            {\n                label: \"month\",\n                seconds: 2592000\n            },\n            {\n                label: \"week\",\n                seconds: 604800\n            },\n            {\n                label: \"day\",\n                seconds: 86400\n            },\n            {\n                label: \"hour\",\n                seconds: 3600\n            },\n            {\n                label: \"minute\",\n                seconds: 60\n            },\n            {\n                label: \"second\",\n                seconds: 1\n            }\n        ];\n        var interval = intervals.find(function(i) {\n            return Math.floor(seconds / i.seconds) > 0;\n        });\n        return interval ? \"\".concat(Math.floor(seconds / interval.seconds), \" \").concat(interval.label).concat(Math.floor(seconds / interval.seconds) !== 1 ? \"s\" : \"\", \" ago\") : \"just now\";\n    };\n    var handleSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function() {\n        var _data_data, status, rest, response;\n        return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    _data_data = data.data, status = _data_data.status, rest = (0,_swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_9__._)(_data_data, [\n                        \"status\"\n                    ]);\n                    return [\n                        4,\n                        (0,common_cms__WEBPACK_IMPORTED_MODULE_10__.putCmsAdminPageDocument)({\n                            kind: kind,\n                            uid: uid,\n                            documentId: (initData === null || initData === void 0 ? void 0 : initData.documentId) || \"\",\n                            data: (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, rest)\n                        })\n                    ];\n                case 1:\n                    response = _state.sent();\n                    setIsRefresh(true);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        data.data,\n        initData === null || initData === void 0 ? void 0 : initData.documentId,\n        kind,\n        uid\n    ]);\n    var handlePublish = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function() {\n        var _data_data, status, rest, response;\n        return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    _data_data = data.data, status = _data_data.status, rest = (0,_swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_9__._)(_data_data, [\n                        \"status\"\n                    ]);\n                    return [\n                        4,\n                        (0,common_cms__WEBPACK_IMPORTED_MODULE_10__.publishCmsAdminPageDocument)({\n                            kind: kind,\n                            uid: uid,\n                            documentId: (initData === null || initData === void 0 ? void 0 : initData.documentId) || \"\",\n                            data: (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, rest)\n                        })\n                    ];\n                case 1:\n                    response = _state.sent();\n                    setIsRefresh(true);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        data.data,\n        initData === null || initData === void 0 ? void 0 : initData.documentId,\n        kind,\n        uid\n    ]);\n    (0,_barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_12__.useIsomorphicLayoutEffect)(function() {\n        if (initData && !isInit) {\n            setData({\n                data: initData\n            });\n            setIsInit(true);\n            return;\n        }\n        var fetchData = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function() {\n                var res;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            return [\n                                4,\n                                (0,common_cms__WEBPACK_IMPORTED_MODULE_10__.getCmsDataAdminClient)({\n                                    path: \"content-manager/single-types/api::\".concat(uid, \".\").concat(uid),\n                                    deep: 4,\n                                    locale: lng,\n                                    ttl: isRefresh ? 0 : 60\n                                })\n                            ];\n                        case 1:\n                            res = _state.sent();\n                            setData(res);\n                            if (isRefresh) setIsRefresh(false);\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function fetchData() {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        fetchData();\n    }, [\n        uid,\n        lng,\n        initData,\n        isInit,\n        isRefresh\n    ]);\n    // console.log(lng, uid)\n    // useIsomorphicLayoutEffect(() => {\n    // \tconsole.log('Get updated data:', data)\n    // }, [data])\n    if (!cmsData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().wrapper),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().layout__editor),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().editor),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().editor__section),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                            className: \"collect__heading collect__heading--h6\",\n                            children: \"Loading...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 8\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 7\n                    }, _this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 6\n                }, _this)\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                lineNumber: 126,\n                columnNumber: 5\n            }, _this)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n            lineNumber: 125,\n            columnNumber: 4\n        }, _this);\n    }\n    // console.log(context)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().layout__editor),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().editor),\n                    children: cmsData.components.map(function(component, idx) {\n                        var cmpData = components.data.find(function(comp) {\n                            return comp.uid === component.__component;\n                        });\n                        if (!cmpData) return null // Không có component tương ứng\n                        ;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().editor__section),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                    className: \"collect__heading collect__heading--h6\",\n                                    children: cmpData.schema.displayName || cmpData.uid\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 9\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().editor__components),\n                                    children: filteredComponents(cmpData.schema.attributes).map(function(param) {\n                                        var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), key = _param[0], value = _param[1];\n                                        var _fieldSizes_val_type;\n                                        var val = value;\n                                        // console.log(key, value, component[key])\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_13__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_14__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, val), {\n                                            name: \"\".concat(key, \" \").concat(Array.isArray(component[key]) ? \"(\".concat(component[key].length, \")\") : \"\"),\n                                            size: (_fieldSizes_val_type = fieldSizes[val.type]) === null || _fieldSizes_val_type === void 0 ? void 0 : _fieldSizes_val_type[\"default\"],\n                                            value: component[key],\n                                            onChange: function(props) {\n                                                setData(function(prevData) {\n                                                    var newData = (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, prevData);\n                                                    var field = props.field, value = props.value;\n                                                    var curCmpIndex = newData.data.components.findIndex(function(data) {\n                                                        return data.__component === component.__component && data.id === component.id;\n                                                    });\n                                                    if (curCmpIndex === -1) {\n                                                        return newData;\n                                                    }\n                                                    newData.data.components[curCmpIndex] = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_14__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, newData.data.components[curCmpIndex]), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_15__._)({}, field.trim(), value));\n                                                    return newData;\n                                                });\n                                            }\n                                        }), key, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 12\n                                        }, _this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 9\n                                }, _this)\n                            ]\n                        }, idx, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 8\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 5\n                }, _this)\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                lineNumber: 139,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().layout__info),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"collect__buttons\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"collect__button\", \"collect__button--lg\", \"black\"),\n                                onClick: function() {\n                                    var newPath = pathname.replace(\"content-manager\", \"content-builder\").split(\"/\");\n                                    if (newPath[newPath.length - 1] === \"edit\") {\n                                        newPath.pop();\n                                    }\n                                    router.push(newPath.join(\"/\"));\n                                },\n                                children: \"Switch to Builder Mode\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 6\n                            }, _this),\n                            isDraftEnable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"collect__button\", \"collect__button--lg\"),\n                                onClick: handleSave,\n                                children: \"Save as Draft\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 7\n                            }, _this),\n                            isDraftEnable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"collect__button\", \"collect__button--lg\", \"yellow\"),\n                                onClick: handlePublish,\n                                children: \"Publish\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 7\n                            }, _this),\n                            !isDraftEnable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"collect__button\", \"collect__button--lg\", \"yellow\"),\n                                onClick: handleSave,\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().lastupdate),\n                                children: [\n                                    \"Updated \",\n                                    updatedTimeConvert(cmsData.updatedAt)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__header),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Page Info\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 7\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"collect__button\", \"collect__button--md\", \"yellow\"),\n                                        onClick: function() {\n                                            return setIsEdit(true);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_17__.Icon, {\n                                                type: \"cms\",\n                                                variant: \"edit\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 8\n                                            }, _this),\n                                            \"Edit\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 7\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__body),\n                                children: filteredInfo(cmsData).map(function(param) {\n                                    var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), key = _param[0], value = _param[1];\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__item),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__label),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: key\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 10\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_17__.Icon, {\n                                                        type: \"cms\",\n                                                        variant: \"info\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 10\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 9\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().info__value)),\n                                                children: typeof value === \"string\" || typeof value === \"number\" ? value : \"unformated data\" // : JSON.stringify(value)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 9\n                                            }, _this)\n                                        ]\n                                    }, key, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 8\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                lineNumber: 192,\n                columnNumber: 4\n            }, _this),\n            isEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().layout__popup),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().popup),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().popup__header),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: function() {\n                                        return setIsEdit(false);\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_17__.Icon, {\n                                            type: \"cms\",\n                                            variant: \"chevron-left\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 9\n                                        }, _this),\n                                        \" Back\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 8\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().popup__title),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"collect__heading\",\n                                            children: \"Edit Page Info\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 9\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                            onClick: function() {\n                                                return setIsEdit(false);\n                                            },\n                                            className: \"collect__button yellow\",\n                                            children: \"Save Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 9\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_singletypelayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().popup__body),\n                            children: filteredInfo(cmsData).map(function(param) {\n                                var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), key = _param[0], value = _param[1];\n                                var _contentTypes_key, _fieldSizes_contentTypes_key_type, _contentTypes_key1;\n                                if (!((_contentTypes_key = contentTypes[key]) === null || _contentTypes_key === void 0 ? void 0 : _contentTypes_key.type)) return null;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_13__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_14__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, contentTypes[key]), {\n                                    name: key,\n                                    size: (_fieldSizes_contentTypes_key_type = fieldSizes[(_contentTypes_key1 = contentTypes[key]) === null || _contentTypes_key1 === void 0 ? void 0 : _contentTypes_key1.type]) === null || _fieldSizes_contentTypes_key_type === void 0 ? void 0 : _fieldSizes_contentTypes_key_type[\"default\"],\n                                    value: value,\n                                    onChange: function(props) {\n                                        setData(function(prevData) {\n                                            var newData = (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, prevData);\n                                            var field = props.field, value = props.value;\n                                            newData.data = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_14__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, newData.data), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_15__._)({}, field.trim(), value));\n                                            return newData;\n                                        });\n                                    }\n                                }), key, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 10\n                                }, _this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 7\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 6\n                }, _this)\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n                lineNumber: 263,\n                columnNumber: 5\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\content\\\\SingleTypeLayout.tsx\",\n        lineNumber: 138,\n        columnNumber: 3\n    }, _this);\n};\n_s(SingleTypeLayout, \"BVNLR05fIe+U9WJCRl1YIiZTZjs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _barrel_optimize_names_Button_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_12__.useIsomorphicLayoutEffect\n    ];\n});\n_c = SingleTypeLayout;\nvar _c;\n$RefreshReg$(_c, \"SingleTypeLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/content/SingleTypeLayout.tsx\n"));

/***/ })

});