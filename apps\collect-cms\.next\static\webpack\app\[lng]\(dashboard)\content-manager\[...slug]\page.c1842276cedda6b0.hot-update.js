"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/Media.tsx":
/*!********************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/Media/Media.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: function() { return /* binding */ Media; },\n/* harmony export */   checkArr: function() { return /* binding */ checkArr; },\n/* harmony export */   formatDate: function() { return /* binding */ formatDate; },\n/* harmony export */   formatExt: function() { return /* binding */ formatExt; },\n/* harmony export */   useMediaHandlers: function() { return /* binding */ useMediaHandlers; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Image/ImageV2.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/../../node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./media.module.scss */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/media.module.scss\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_media_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _MediaInfoLayer__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./MediaInfoLayer */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/MediaInfoLayer.tsx\");\n\n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar formatDate = function(date) {\n    return dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"D/M/YYYY\");\n};\nvar formatExt = function(ext) {\n    return ext.replace(\".\", \"\");\n};\nvar checkArr = function(value) {\n    return Array.isArray(value);\n};\n// Custom hook for shared media logic\nvar useMediaHandlers = function(propsValue, setPropsValue, currentMediaIdx, setCurrentMediaIdx, multiple, onChange, field) {\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_4__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__.PageBuilderContext);\n    var setMediaInfoData = context.setMediaInfoData, setActiveMediaId = context.setActiveMediaId;\n    // Utility functions\n    var createFileInput = function() {\n        var multiple = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        var input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"image/*,video/*,audio/*,.pdf,.doc,.docx\";\n        input.multiple = multiple;\n        return input;\n    };\n    var validateFileSize = function(file) {\n        var maxSize = 20 * 1024 * 1024 // 20MB\n        ;\n        var fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);\n        if (file.size > maxSize) {\n            return {\n                isValid: false,\n                errorMessage: 'File \"'.concat(file.name, '\" (').concat(fileSizeMB, \"MB) exceeds the 20MB limit.\")\n            };\n        }\n        return {\n            isValid: true\n        };\n    };\n    var fileToMediaProps = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(file) {\n            var _file_name_split_pop, url, width, height, dimensions, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        url = URL.createObjectURL(file);\n                        if (!file.type.startsWith(\"image/\")) return [\n                            3,\n                            4\n                        ];\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            getImageDimensions(url)\n                        ];\n                    case 2:\n                        dimensions = _state.sent();\n                        width = dimensions.width;\n                        height = dimensions.height;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.warn(\"Failed to get image dimensions:\", error);\n                        // Default dimensions for images if we can't read them\n                        width = 800;\n                        height = 600;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2,\n                            {\n                                id: Date.now() + Math.random(),\n                                name: file.name,\n                                url: url,\n                                ext: \".\" + ((_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase()),\n                                mime: file.type,\n                                size: file.size,\n                                alternativeText: file.name,\n                                width: width,\n                                height: height\n                            }\n                        ];\n                }\n            });\n        });\n        return function fileToMediaProps(file) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var getImageDimensions = function(url) {\n        return new Promise(function(resolve, reject) {\n            var img = document.createElement(\"img\");\n            img.onload = function() {\n                resolve({\n                    width: img.naturalWidth,\n                    height: img.naturalHeight\n                });\n            };\n            img.onerror = reject;\n            img.src = url;\n        });\n    };\n    var updatePropsValue = function(newValue) {\n        setPropsValue(newValue);\n        if (onChange) {\n            onChange({\n                field: field || \"media\",\n                value: newValue\n            });\n        }\n    };\n    var handleNextMedia = function() {\n        if (checkArr(propsValue) && propsValue.length > 0) {\n            setCurrentMediaIdx(function(prevIdx) {\n                return prevIdx + 1 < propsValue.length ? prevIdx + 1 : 0;\n            });\n        }\n    };\n    var handlePrevMedia = function() {\n        if (checkArr(propsValue) && propsValue.length > 0) {\n            setCurrentMediaIdx(function(prevIdx) {\n                return prevIdx - 1 >= 0 ? prevIdx - 1 : propsValue.length - 1;\n            });\n        }\n    };\n    var handleAdd = function() {\n        var input = createFileInput(multiple);\n        input.onchange = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(e) {\n                var files, validFiles, invalidFiles, newMediaItems, currentArray, newValue;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            files = e.target.files;\n                            if (!files) return [\n                                2\n                            ];\n                            validFiles = [];\n                            invalidFiles = [];\n                            // Validate each file\n                            Array.from(files).forEach(function(file) {\n                                var validation = validateFileSize(file);\n                                if (validation.isValid) {\n                                    validFiles.push(file);\n                                } else {\n                                    invalidFiles.push(validation.errorMessage || \"File \".concat(file.name, \" is invalid\"));\n                                }\n                            });\n                            // Show error messages for invalid files\n                            if (invalidFiles.length > 0) {\n                                alert(\"The following files were skipped:\\n\\n\".concat(invalidFiles.join(\"\\n\")));\n                            }\n                            if (!(validFiles.length > 0)) return [\n                                3,\n                                2\n                            ];\n                            return [\n                                4,\n                                Promise.all(validFiles.map(fileToMediaProps))\n                            ];\n                        case 1:\n                            newMediaItems = _state.sent();\n                            if (multiple) {\n                                currentArray = checkArr(propsValue) ? propsValue : [];\n                                newValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(currentArray).concat((0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(newMediaItems));\n                                updatePropsValue(newValue);\n                            } else {\n                                updatePropsValue(newMediaItems[0]);\n                            }\n                            _state.label = 2;\n                        case 2:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(e) {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        input.click();\n    };\n    var handleReplace = function() {\n        var input = createFileInput(false);\n        input.onchange = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(e) {\n                var files, validation, newMedia, newArray;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            files = e.target.files;\n                            if (!files || !files[0]) return [\n                                2\n                            ];\n                            validation = validateFileSize(files[0]);\n                            if (!validation.isValid) {\n                                alert(validation.errorMessage || \"File exceeds the 20MB limit.\");\n                                return [\n                                    2\n                                ];\n                            }\n                            return [\n                                4,\n                                fileToMediaProps(files[0])\n                            ];\n                        case 1:\n                            newMedia = _state.sent();\n                            if (multiple && checkArr(propsValue)) {\n                                newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n                                newArray[currentMediaIdx] = newMedia;\n                                updatePropsValue(newArray);\n                            } else {\n                                updatePropsValue(newMedia);\n                            }\n                            // Update context\n                            setMediaInfoData(newMedia);\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(e) {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        input.click();\n    };\n    var handleDuplicate = function() {\n        var currentMedia = checkArr(propsValue) ? propsValue[currentMediaIdx] : propsValue;\n        if (!currentMedia) return;\n        var duplicatedMedia = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, currentMedia), {\n            id: Date.now() + Math.random(),\n            name: \"The copy of \".concat(currentMedia.name)\n        });\n        if (multiple && checkArr(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n            newArray.splice(currentMediaIdx + 1, 0, duplicatedMedia);\n            updatePropsValue(newArray);\n        } else {\n            // For single mode, replace current with duplicate\n            updatePropsValue(duplicatedMedia);\n        }\n    };\n    var handleRemove = function() {\n        if (multiple && checkArr(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n            newArray.splice(currentMediaIdx, 1);\n            // Reset editing if removing currently edited media\n            setActiveMediaId(null);\n            setMediaInfoData({\n                name: \"\",\n                url: \"\"\n            });\n            // Adjust current index if needed\n            if (currentMediaIdx >= newArray.length && newArray.length > 0) {\n                setCurrentMediaIdx(newArray.length - 1);\n            }\n            updatePropsValue(newArray);\n        } else {\n            // For single mode, clear the media\n            setActiveMediaId(null);\n            setMediaInfoData({\n                name: \"\",\n                url: \"\"\n            });\n            updatePropsValue(null);\n        }\n    };\n    var handleDownload = function() {\n        var currentMedia = checkArr(propsValue) ? propsValue[currentMediaIdx] : propsValue;\n        if (!(currentMedia === null || currentMedia === void 0 ? void 0 : currentMedia.url)) return;\n        var link = document.createElement(\"a\");\n        link.href = currentMedia.url;\n        link.download = currentMedia.name || \"download\";\n        link.target = \"_blank\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    };\n    var handleAction = function(key) {\n        switch(key){\n            case \"add\":\n                handleAdd();\n                break;\n            case \"replace\":\n                handleReplace();\n                break;\n            case \"duplicate\":\n                handleDuplicate();\n                break;\n            case \"remove\":\n                handleRemove();\n                break;\n            case \"download\":\n                handleDownload();\n                break;\n            default:\n                break;\n        }\n    };\n    return {\n        handleAdd: handleAdd,\n        handleReplace: handleReplace,\n        handleDuplicate: handleDuplicate,\n        handleRemove: handleRemove,\n        handleDownload: handleDownload,\n        handleAction: handleAction,\n        handleNextMedia: handleNextMedia,\n        handlePrevMedia: handlePrevMedia\n    };\n};\n_s(useMediaHandlers, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar Media = function(props) {\n    _s1();\n    var _ref = props !== null && props !== void 0 ? props : {}, value = _ref.value, onChange = _ref.onChange, multiple = _ref.multiple;\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_4__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__.PageBuilderContext);\n    var mediaInfoData = context.mediaInfoData, setMediaInfoData = context.setMediaInfoData, activeMediaId = context.activeMediaId, setActiveMediaId = context.setActiveMediaId;\n    var mediaId = (0,react__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false), 2), isEdit = _useState[0], setisEdit = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(multiple ? value : value), 2), propsValue = _useState1[0], setPropsValue = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(checkArr(propsValue) ? propsValue[0] : propsValue), 2), currentMedia = _useState2[0], setCurrentMedia = _useState2[1];\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0), 2), currentMediaIdx = _useState3[0], setCurrentMediaIdx = _useState3[1];\n    // Use shared media handlers\n    var _useMediaHandlers = useMediaHandlers(propsValue, setPropsValue, currentMediaIdx, setCurrentMediaIdx, multiple, onChange, props.field), handleAdd = _useMediaHandlers.handleAdd, handleReplace = _useMediaHandlers.handleReplace, handleDuplicate = _useMediaHandlers.handleDuplicate, handleRemove = _useMediaHandlers.handleRemove, handleDownload = _useMediaHandlers.handleDownload, handleAction = _useMediaHandlers.handleAction, handleNextMedia = _useMediaHandlers.handleNextMedia, handlePrevMedia = _useMediaHandlers.handlePrevMedia;\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        if (checkArr(propsValue)) {\n            setCurrentMedia(propsValue[currentMediaIdx]);\n            setMediaInfoData(propsValue[currentMediaIdx]);\n        } else {\n            setCurrentMedia(propsValue);\n            setMediaInfoData(propsValue);\n        }\n    }, [\n        currentMediaIdx,\n        propsValue\n    ]);\n    // useIsomorphicLayoutEffect(() => {\n    // \tif (isEdit && currentMedia) {\n    // \t\thandleEdit()\n    // \t}\n    // }, [currentMedia])\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        mediaInfoData && mediaInfoData.name === \"\" && setisEdit(false);\n    }, [\n        mediaInfoData\n    ]);\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        setisEdit(activeMediaId === mediaId);\n    }, [\n        activeMediaId,\n        mediaId\n    ]);\n    var mediaToolbar = [\n        {\n            name: \"Add\",\n            icon: \"add\",\n            action: \"add\",\n            visible: !multiple\n        },\n        {\n            name: \"Replace\",\n            icon: \"replace\",\n            action: \"replace\"\n        },\n        {\n            name: \"Duplicate\",\n            icon: \"duplicate\",\n            action: \"duplicate\",\n            visible: !multiple\n        },\n        {\n            name: \"Remove\",\n            icon: \"remove\",\n            action: \"remove\"\n        },\n        {\n            name: \"Download\",\n            icon: \"download\",\n            action: \"download\",\n            visible: !isEdit\n        }\n    ];\n    var filteredMediaToolbar = mediaToolbar.filter(function(tool) {\n        return !tool.visible;\n    });\n    var handleEdit = function() {\n        console.log(currentMedia);\n        setMediaInfoData(currentMedia);\n        setActiveMediaId(mediaId);\n    };\n    var handleBack = function() {\n        setActiveMediaId(null);\n        setMediaInfoData({\n            name: \"\",\n            url: \"\"\n        });\n    };\n    var isBuilderMode = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function() {\n        return pathname === null || pathname === void 0 ? void 0 : pathname.startsWith(\"/content-builder/\");\n    }, [\n        pathname\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().controller),\n                style: {\n                    \"--controller-cols\": isBuilderMode ? 12 : 8\n                },\n                children: [\n                    multiple && !isEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__btn),\n                                onClick: handlePrevMedia,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"chevron-left\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__index),\n                                children: \"\".concat(currentMediaIdx + 1, \"/\").concat(checkArr(propsValue) ? propsValue.length : 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__btn),\n                                onClick: handleNextMedia,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"chevron-right\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 6\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().body), !isBuilderMode && isEdit ? multiple ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().detailed__multi) : (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().detailed) : \"\"),\n                        children: [\n                            currentMedia ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().item),\n                                style: {\n                                    \"--height\": isBuilderMode ? \"160px\" : \"280px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().tag),\n                                        children: formatExt((currentMedia === null || currentMedia === void 0 ? void 0 : currentMedia.ext) || \"\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().thumbnail),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Image, {\n                                            media: currentMedia,\n                                            alt: \"\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 8\n                                    }, _this),\n                                    !isEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().mask),\n                                        title: \"Edit this media\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                            onClick: function() {\n                                                return handleEdit();\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                                type: \"cms\",\n                                                variant: \"edit\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 11\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 10\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 9\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 7\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().empty),\n                                style: {\n                                    \"--height\": isBuilderMode ? \"160px\" : \"280px\"\n                                },\n                                title: \"Browse file(s)\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                        type: \"cms\",\n                                        variant: \"image\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Drop your file(s) here or\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                                onClick: function() {\n                                                    return handleAction(\"add\");\n                                                },\n                                                children: \"browse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 9\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                        children: \"Max. File Size: 20MB\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 8\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 7\n                            }, _this),\n                            isEdit && checkArr(propsValue) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__nav),\n                                        onClick: handlePrevMedia,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: \"chevron-left\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__list),\n                                        children: propsValue.map(function(media, idx) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__thumb), idx === currentMediaIdx ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"),\n                                                onClick: function() {\n                                                    return setCurrentMediaIdx(idx);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Image, {\n                                                    media: media,\n                                                    alt: \"\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 11\n                                                }, _this)\n                                            }, idx, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 10\n                                            }, _this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__nav),\n                                        onClick: handleNextMedia,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: \"chevron-right\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 8\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 5\n                    }, _this),\n                    !isBuilderMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__list),\n                                children: filteredMediaToolbar.map(function(tool, idx) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button),\n                                        onClick: function() {\n                                            return handleAction(tool.action);\n                                        },\n                                        title: tool.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: tool.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 10\n                                        }, _this)\n                                    }, idx, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 9\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__fixed),\n                                children: !isEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button), (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().text)),\n                                    title: \"Edit\",\n                                    onClick: handleEdit,\n                                    children: \"Edit\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 9\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button), (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().text)),\n                                    title: \"Back\",\n                                    onClick: handleBack,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                        type: \"cms\",\n                                        variant: \"back\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 10\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 9\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 6\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                lineNumber: 393,\n                columnNumber: 4\n            }, _this),\n            isEdit && activeMediaId === mediaId && mediaInfoData && mediaInfoData.name !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MediaInfoLayer__WEBPACK_IMPORTED_MODULE_17__.MediaInfoLayer, {\n                multiple: multiple,\n                toolbar: filteredMediaToolbar,\n                mediaList: propsValue\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                lineNumber: 523,\n                columnNumber: 5\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n        lineNumber: 392,\n        columnNumber: 3\n    }, _this);\n};\n_s1(Media, \"mYS4NrXU5R1aKmub3BD2op6MkKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        react__WEBPACK_IMPORTED_MODULE_4__.useId,\n        useMediaHandlers,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect\n    ];\n});\n_c = Media;\nvar _c;\n$RefreshReg$(_c, \"Media\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/Media.tsx\n"));

/***/ })

});