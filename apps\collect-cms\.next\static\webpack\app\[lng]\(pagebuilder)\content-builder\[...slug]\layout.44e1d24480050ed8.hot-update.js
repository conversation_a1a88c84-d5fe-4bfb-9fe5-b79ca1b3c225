"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/Media.tsx":
/*!********************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/Media/Media.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: function() { return /* binding */ Media; },\n/* harmony export */   checkArr: function() { return /* binding */ checkArr; },\n/* harmony export */   formatDate: function() { return /* binding */ formatDate; },\n/* harmony export */   formatExt: function() { return /* binding */ formatExt; },\n/* harmony export */   useMediaHandlers: function() { return /* binding */ useMediaHandlers; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Image/ImageV2.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/../../node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./media.module.scss */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/media.module.scss\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_media_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _MediaInfoLayer__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./MediaInfoLayer */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/MediaInfoLayer.tsx\");\n\n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar formatDate = function(date) {\n    return dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"D/M/YYYY\");\n};\nvar formatExt = function(ext) {\n    return ext.replace(\".\", \"\");\n};\nvar checkArr = function(value) {\n    return Array.isArray(value);\n};\n// Custom hook for shared media logic\nvar useMediaHandlers = function(propsValue, setPropsValue, currentMediaIdx, setCurrentMediaIdx, multiple, onChange, field) {\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_4__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__.PageBuilderContext);\n    var setMediaInfoData = context.setMediaInfoData, setActiveMediaId = context.setActiveMediaId;\n    // Utility functions\n    var createFileInput = function() {\n        var multiple = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        var input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"image/*,video/*,audio/*,.pdf,.doc,.docx\";\n        input.multiple = multiple;\n        return input;\n    };\n    var validateFileSize = function(file) {\n        var maxSize = 20 * 1024 * 1024 // 20MB\n        ;\n        var fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);\n        if (file.size > maxSize) {\n            return {\n                isValid: false,\n                errorMessage: 'File \"'.concat(file.name, '\" (').concat(fileSizeMB, \"MB) exceeds the 20MB limit.\")\n            };\n        }\n        return {\n            isValid: true\n        };\n    };\n    var fileToMediaProps = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(file) {\n            var _file_name_split_pop, url, width, height, dimensions, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        url = URL.createObjectURL(file);\n                        if (!file.type.startsWith(\"image/\")) return [\n                            3,\n                            4\n                        ];\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            getImageDimensions(url)\n                        ];\n                    case 2:\n                        dimensions = _state.sent();\n                        width = dimensions.width;\n                        height = dimensions.height;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.warn(\"Failed to get image dimensions:\", error);\n                        // Default dimensions for images if we can't read them\n                        width = 800;\n                        height = 600;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2,\n                            {\n                                id: Date.now() + Math.random(),\n                                name: file.name,\n                                url: url,\n                                ext: \".\" + ((_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase()),\n                                mime: file.type,\n                                size: file.size,\n                                alternativeText: file.name,\n                                width: width,\n                                height: height\n                            }\n                        ];\n                }\n            });\n        });\n        return function fileToMediaProps(file) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var getImageDimensions = function(url) {\n        return new Promise(function(resolve, reject) {\n            var img = document.createElement(\"img\");\n            img.onload = function() {\n                resolve({\n                    width: img.naturalWidth,\n                    height: img.naturalHeight\n                });\n            };\n            img.onerror = reject;\n            img.src = url;\n        });\n    };\n    var updatePropsValue = function(newValue) {\n        setPropsValue(newValue);\n        if (onChange) {\n            onChange({\n                field: field || \"media\",\n                value: newValue\n            });\n        }\n    };\n    var handleNextMedia = function() {\n        if (checkArr(propsValue) && propsValue.length > 0) {\n            setCurrentMediaIdx(function(prevIdx) {\n                return prevIdx + 1 < propsValue.length ? prevIdx + 1 : 0;\n            });\n        }\n    };\n    var handlePrevMedia = function() {\n        if (checkArr(propsValue) && propsValue.length > 0) {\n            setCurrentMediaIdx(function(prevIdx) {\n                return prevIdx - 1 >= 0 ? prevIdx - 1 : propsValue.length - 1;\n            });\n        }\n    };\n    var handleAdd = function() {\n        var input = createFileInput(multiple);\n        input.onchange = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(e) {\n                var files, validFiles, invalidFiles, newMediaItems, currentArray, newValue;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            files = e.target.files;\n                            if (!files) return [\n                                2\n                            ];\n                            validFiles = [];\n                            invalidFiles = [];\n                            // Validate each file\n                            Array.from(files).forEach(function(file) {\n                                var validation = validateFileSize(file);\n                                if (validation.isValid) {\n                                    validFiles.push(file);\n                                } else {\n                                    invalidFiles.push(validation.errorMessage || \"File \".concat(file.name, \" is invalid\"));\n                                }\n                            });\n                            // Show error messages for invalid files\n                            if (invalidFiles.length > 0) {\n                                alert(\"The following files were skipped:\\n\\n\".concat(invalidFiles.join(\"\\n\")));\n                            }\n                            if (!(validFiles.length > 0)) return [\n                                3,\n                                2\n                            ];\n                            return [\n                                4,\n                                Promise.all(validFiles.map(fileToMediaProps))\n                            ];\n                        case 1:\n                            newMediaItems = _state.sent();\n                            if (multiple) {\n                                currentArray = checkArr(propsValue) ? propsValue : [];\n                                newValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(currentArray).concat((0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(newMediaItems));\n                                updatePropsValue(newValue);\n                            } else {\n                                updatePropsValue(newMediaItems[0]);\n                            }\n                            _state.label = 2;\n                        case 2:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(e) {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        input.click();\n    };\n    var handleReplace = function() {\n        var input = createFileInput(false);\n        input.onchange = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(e) {\n                var files, validation, newMedia, newArray;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            files = e.target.files;\n                            if (!files || !files[0]) return [\n                                2\n                            ];\n                            validation = validateFileSize(files[0]);\n                            if (!validation.isValid) {\n                                alert(validation.errorMessage || \"File exceeds the 20MB limit.\");\n                                return [\n                                    2\n                                ];\n                            }\n                            return [\n                                4,\n                                fileToMediaProps(files[0])\n                            ];\n                        case 1:\n                            newMedia = _state.sent();\n                            if (multiple && checkArr(propsValue)) {\n                                newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n                                newArray[currentMediaIdx] = newMedia;\n                                updatePropsValue(newArray);\n                            } else {\n                                updatePropsValue(newMedia);\n                            }\n                            // Update context\n                            setMediaInfoData(newMedia);\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(e) {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        input.click();\n    };\n    var handleDuplicate = function() {\n        var currentMedia = checkArr(propsValue) ? propsValue[currentMediaIdx] : propsValue;\n        if (!currentMedia) return;\n        var duplicatedMedia = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, currentMedia), {\n            id: Date.now() + Math.random(),\n            name: \"The copy of \".concat(currentMedia.name)\n        });\n        if (multiple && checkArr(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n            newArray.splice(currentMediaIdx + 1, 0, duplicatedMedia);\n            updatePropsValue(newArray);\n        } else {\n            // For single mode, replace current with duplicate\n            updatePropsValue(duplicatedMedia);\n        }\n    };\n    var handleRemove = function() {\n        if (multiple && checkArr(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n            newArray.splice(currentMediaIdx, 1);\n            // Reset editing if removing currently edited media\n            setActiveMediaId(null);\n            setMediaInfoData({\n                name: \"\",\n                url: \"\"\n            });\n            // Adjust current index if needed\n            if (currentMediaIdx >= newArray.length && newArray.length > 0) {\n                setCurrentMediaIdx(newArray.length - 1);\n            }\n            updatePropsValue(newArray);\n        } else {\n            // For single mode, clear the media\n            setActiveMediaId(null);\n            setMediaInfoData({\n                name: \"\",\n                url: \"\"\n            });\n            updatePropsValue(null);\n        }\n    };\n    var handleDownload = function() {\n        var currentMedia = checkArr(propsValue) ? propsValue[currentMediaIdx] : propsValue;\n        if (!(currentMedia === null || currentMedia === void 0 ? void 0 : currentMedia.url)) return;\n        var link = document.createElement(\"a\");\n        link.href = currentMedia.url;\n        link.download = currentMedia.name || \"download\";\n        link.target = \"_blank\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    };\n    var handleAction = function(key) {\n        switch(key){\n            case \"add\":\n                handleAdd();\n                break;\n            case \"replace\":\n                handleReplace();\n                break;\n            case \"duplicate\":\n                handleDuplicate();\n                break;\n            case \"remove\":\n                handleRemove();\n                break;\n            case \"download\":\n                handleDownload();\n                break;\n            default:\n                break;\n        }\n    };\n    return {\n        handleAdd: handleAdd,\n        handleReplace: handleReplace,\n        handleDuplicate: handleDuplicate,\n        handleRemove: handleRemove,\n        handleDownload: handleDownload,\n        handleAction: handleAction,\n        handleNextMedia: handleNextMedia,\n        handlePrevMedia: handlePrevMedia\n    };\n};\n_s(useMediaHandlers, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar Media = function(props) {\n    _s1();\n    var _ref = props !== null && props !== void 0 ? props : {}, value = _ref.value, onChange = _ref.onChange, multiple = _ref.multiple;\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_4__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__.PageBuilderContext);\n    var mediaInfoData = context.mediaInfoData, setMediaInfoData = context.setMediaInfoData, activeMediaId = context.activeMediaId, setActiveMediaId = context.setActiveMediaId;\n    var mediaId = (0,react__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false), 2), isEdit = _useState[0], setisEdit = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(multiple ? value : value), 2), propsValue = _useState1[0], setPropsValue = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(checkArr(propsValue) ? propsValue[0] : propsValue), 2), currentMedia = _useState2[0], setCurrentMedia = _useState2[1];\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0), 2), currentMediaIdx = _useState3[0], setCurrentMediaIdx = _useState3[1];\n    var handleNextMedia = function() {\n        if (checkArr(propsValue) && propsValue.length > 0) {\n            setCurrentMediaIdx(function(prevIdx) {\n                return prevIdx + 1 < propsValue.length ? prevIdx + 1 : 0;\n            });\n        }\n    };\n    var handlePrevMedia = function() {\n        if (checkArr(propsValue) && propsValue.length > 0) {\n            setCurrentMediaIdx(function(prevIdx) {\n                return prevIdx - 1 >= 0 ? prevIdx - 1 : propsValue.length - 1;\n            });\n        }\n    };\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        if (checkArr(propsValue)) {\n            setCurrentMedia(propsValue[currentMediaIdx]);\n            setMediaInfoData(propsValue[currentMediaIdx]);\n        } else {\n            setCurrentMedia(propsValue);\n            setMediaInfoData(propsValue);\n        }\n    }, [\n        currentMediaIdx,\n        propsValue\n    ]);\n    // useIsomorphicLayoutEffect(() => {\n    // \tif (isEdit && currentMedia) {\n    // \t\thandleEdit()\n    // \t}\n    // }, [currentMedia])\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        mediaInfoData && mediaInfoData.name === \"\" && setisEdit(false);\n    }, [\n        mediaInfoData\n    ]);\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        setisEdit(activeMediaId === mediaId);\n    }, [\n        activeMediaId,\n        mediaId\n    ]);\n    var mediaToolbar = [\n        {\n            name: \"Add\",\n            icon: \"add\",\n            action: \"add\",\n            visible: !multiple\n        },\n        {\n            name: \"Replace\",\n            icon: \"replace\",\n            action: \"replace\"\n        },\n        {\n            name: \"Duplicate\",\n            icon: \"duplicate\",\n            action: \"duplicate\",\n            visible: !multiple\n        },\n        {\n            name: \"Remove\",\n            icon: \"remove\",\n            action: \"remove\"\n        },\n        {\n            name: \"Download\",\n            icon: \"download\",\n            action: \"download\",\n            visible: !isEdit\n        }\n    ];\n    var filteredMediaToolbar = mediaToolbar.filter(function(tool) {\n        return !tool.visible;\n    });\n    var handleEdit = function() {\n        console.log(currentMedia);\n        setMediaInfoData(currentMedia);\n        setActiveMediaId(mediaId);\n    };\n    var handleBack = function() {\n        setActiveMediaId(null);\n        setMediaInfoData({\n            name: \"\",\n            url: \"\"\n        });\n    };\n    var handleAction = function(key) {\n        switch(key){\n            case \"add\":\n                handleAdd();\n                break;\n            case \"replace\":\n                handleReplace();\n                break;\n            case \"duplicate\":\n                handleDuplicate();\n                break;\n            case \"remove\":\n                handleRemove();\n                break;\n            case \"download\":\n                handleDownload();\n                break;\n            default:\n                break;\n        }\n    };\n    // Utility functions\n    var createFileInput = function() {\n        var multiple = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        var input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"image/*,video/*\";\n        input.multiple = multiple;\n        return input;\n    };\n    var validateFileSize = function(file) {\n        var maxSize = 20 * 1024 * 1024 // 20MB\n        ;\n        var fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);\n        if (file.size > maxSize) {\n            return {\n                isValid: false,\n                errorMessage: 'File \"'.concat(file.name, '\" (').concat(fileSizeMB, \"MB) exceeds the 20MB limit.\")\n            };\n        }\n        return {\n            isValid: true\n        };\n    };\n    var fileToMediaProps = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(file) {\n            var _file_name_split_pop, url, width, height, dimensions, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        url = URL.createObjectURL(file);\n                        if (!file.type.startsWith(\"image/\")) return [\n                            3,\n                            4\n                        ];\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            getImageDimensions(url)\n                        ];\n                    case 2:\n                        dimensions = _state.sent();\n                        width = dimensions.width;\n                        height = dimensions.height;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.warn(\"Failed to get image dimensions:\", error);\n                        // Default dimensions for images if we can't read them\n                        width = 800;\n                        height = 600;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2,\n                            {\n                                id: Date.now() + Math.random(),\n                                name: file.name,\n                                url: url,\n                                ext: \".\" + ((_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase()),\n                                mime: file.type,\n                                size: file.size,\n                                alternativeText: file.name,\n                                width: width,\n                                height: height\n                            }\n                        ];\n                }\n            });\n        });\n        return function fileToMediaProps(file) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var getImageDimensions = function(url) {\n        return new Promise(function(resolve, reject) {\n            var img = document.createElement(\"img\");\n            img.onload = function() {\n                resolve({\n                    width: img.naturalWidth,\n                    height: img.naturalHeight\n                });\n            };\n            img.onerror = reject;\n            img.src = url;\n        });\n    };\n    var updatePropsValue = function(newValue) {\n        setPropsValue(newValue);\n        onChange({\n            field: props.field || \"media\",\n            value: newValue\n        });\n    };\n    var handleAdd = function() {\n        var input = createFileInput(multiple);\n        input.onchange = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(e) {\n                var files, validFiles, invalidFiles, newMediaItems, currentArray, newValue;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            files = e.target.files;\n                            if (!files) return [\n                                2\n                            ];\n                            validFiles = [];\n                            invalidFiles = [];\n                            // Validate each file\n                            Array.from(files).forEach(function(file) {\n                                var validation = validateFileSize(file);\n                                if (validation.isValid) {\n                                    validFiles.push(file);\n                                } else {\n                                    invalidFiles.push(validation.errorMessage || \"File \".concat(file.name, \" is invalid\"));\n                                }\n                            });\n                            // Show error messages for invalid files\n                            if (invalidFiles.length > 0) {\n                                alert(\"The following files were skipped:\\n\\n\".concat(invalidFiles.join(\"\\n\")));\n                            }\n                            if (!(validFiles.length > 0)) return [\n                                3,\n                                2\n                            ];\n                            return [\n                                4,\n                                Promise.all(validFiles.map(fileToMediaProps))\n                            ];\n                        case 1:\n                            newMediaItems = _state.sent();\n                            if (multiple) {\n                                currentArray = checkArr(propsValue) ? propsValue : [];\n                                newValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(currentArray).concat((0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(newMediaItems));\n                                updatePropsValue(newValue);\n                            } else {\n                                updatePropsValue(newMediaItems[0]);\n                            }\n                            _state.label = 2;\n                        case 2:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(e) {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        input.click();\n    };\n    var handleReplace = function() {\n        var input = createFileInput(false);\n        input.onchange = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(e) {\n                var files, validation, newMedia, newArray;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            files = e.target.files;\n                            if (!files || !files[0]) return [\n                                2\n                            ];\n                            validation = validateFileSize(files[0]);\n                            if (!validation.isValid) {\n                                alert(validation.errorMessage || \"File exceeds the 20MB limit.\");\n                                return [\n                                    2\n                                ];\n                            }\n                            return [\n                                4,\n                                fileToMediaProps(files[0])\n                            ];\n                        case 1:\n                            newMedia = _state.sent();\n                            if (multiple && checkArr(propsValue)) {\n                                newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n                                newArray[currentMediaIdx] = newMedia;\n                                updatePropsValue(newArray);\n                            } else {\n                                updatePropsValue(newMedia);\n                            }\n                            // Update context if currently editing this media\n                            if (isEdit) {\n                                setMediaInfoData(newMedia);\n                            }\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(e) {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        input.click();\n    };\n    var handleDuplicate = function() {\n        if (!currentMedia) return;\n        var duplicatedMedia = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, currentMedia), {\n            id: Date.now() + Math.random(),\n            name: \"The copy of \".concat(currentMedia.name)\n        });\n        if (multiple && checkArr(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n            newArray.splice(currentMediaIdx + 1, 0, duplicatedMedia);\n            updatePropsValue(newArray);\n        } else {\n            // For single mode, replace current with duplicate\n            updatePropsValue(duplicatedMedia);\n        }\n    };\n    var handleRemove = function() {\n        if (multiple && checkArr(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n            newArray.splice(currentMediaIdx, 1);\n            // Reset editing if removing currently edited media\n            if (isEdit && currentMedia === propsValue[currentMediaIdx]) {\n                setActiveMediaId(null);\n                setMediaInfoData({\n                    name: \"\",\n                    url: \"\"\n                });\n            }\n            // Adjust current index if needed\n            if (currentMediaIdx >= newArray.length && newArray.length > 0) {\n                setCurrentMediaIdx(newArray.length - 1);\n            }\n            updatePropsValue(newArray);\n        } else {\n            // For single mode, clear the media\n            setActiveMediaId(null);\n            setMediaInfoData({\n                name: \"\",\n                url: \"\"\n            });\n            updatePropsValue(null);\n        }\n    };\n    var handleDownload = function() {\n        if (!(currentMedia === null || currentMedia === void 0 ? void 0 : currentMedia.url)) return;\n        var link = document.createElement(\"a\");\n        link.href = currentMedia.url;\n        link.download = currentMedia.name || \"download\";\n        link.target = \"_blank\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    };\n    var isBuilderMode = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function() {\n        return pathname === null || pathname === void 0 ? void 0 : pathname.startsWith(\"/content-builder/\");\n    }, [\n        pathname\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().controller),\n                style: {\n                    \"--controller-cols\": isBuilderMode ? 12 : 8\n                },\n                children: [\n                    multiple && !isEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__btn),\n                                onClick: handlePrevMedia,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"chevron-left\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__index),\n                                children: \"\".concat(currentMediaIdx + 1, \"/\").concat(checkArr(propsValue) ? propsValue.length : 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__btn),\n                                onClick: handleNextMedia,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"chevron-right\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 6\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().body), !isBuilderMode && isEdit ? multiple ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().detailed__multi) : (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().detailed) : \"\"),\n                        children: [\n                            currentMedia ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().item),\n                                style: {\n                                    \"--height\": isBuilderMode ? \"160px\" : \"280px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().tag),\n                                        children: formatExt((currentMedia === null || currentMedia === void 0 ? void 0 : currentMedia.ext) || \"\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().thumbnail),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Image, {\n                                            media: currentMedia,\n                                            alt: \"\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 646,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 8\n                                    }, _this),\n                                    !isEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().mask),\n                                        title: \"Edit this media\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                            onClick: function() {\n                                                return handleEdit();\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                                type: \"cms\",\n                                                variant: \"edit\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 11\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 10\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 9\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 7\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().empty),\n                                style: {\n                                    \"--height\": isBuilderMode ? \"160px\" : \"280px\"\n                                },\n                                title: \"Browse file(s)\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                        type: \"cms\",\n                                        variant: \"image\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Drop your file(s) here or\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                                onClick: function() {\n                                                    return handleAction(\"add\");\n                                                },\n                                                children: \"browse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 9\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                        children: \"Max. File Size: 20MB\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 8\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 7\n                            }, _this),\n                            isEdit && checkArr(propsValue) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__nav),\n                                        onClick: handlePrevMedia,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: \"chevron-left\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__list),\n                                        children: propsValue.map(function(media, idx) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__thumb), idx === currentMediaIdx ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"),\n                                                onClick: function() {\n                                                    return setCurrentMediaIdx(idx);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Image, {\n                                                    media: media,\n                                                    alt: \"\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 11\n                                                }, _this)\n                                            }, idx, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 10\n                                            }, _this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__nav),\n                                        onClick: handleNextMedia,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: \"chevron-right\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 8\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 5\n                    }, _this),\n                    !isBuilderMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__list),\n                                children: filteredMediaToolbar.map(function(tool, idx) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button),\n                                        onClick: function() {\n                                            return handleAction(tool.action);\n                                        },\n                                        title: tool.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: tool.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 10\n                                        }, _this)\n                                    }, idx, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 9\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 702,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__fixed),\n                                children: !isEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button), (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().text)),\n                                    title: \"Edit\",\n                                    onClick: handleEdit,\n                                    children: \"Edit\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 9\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button), (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().text)),\n                                    title: \"Back\",\n                                    onClick: handleBack,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                        type: \"cms\",\n                                        variant: \"back\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 10\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 9\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 701,\n                        columnNumber: 6\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                lineNumber: 608,\n                columnNumber: 4\n            }, _this),\n            isEdit && activeMediaId === mediaId && mediaInfoData && mediaInfoData.name !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MediaInfoLayer__WEBPACK_IMPORTED_MODULE_17__.MediaInfoLayer, {\n                multiple: multiple,\n                toolbar: filteredMediaToolbar,\n                mediaList: propsValue\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                lineNumber: 738,\n                columnNumber: 5\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n        lineNumber: 607,\n        columnNumber: 3\n    }, _this);\n};\n_s1(Media, \"2Bg914Y4KrgPOPZFAW3dbBvQJ3Q=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        react__WEBPACK_IMPORTED_MODULE_4__.useId,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect\n    ];\n});\n_c = Media;\nvar _c;\n$RefreshReg$(_c, \"Media\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/Media.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/index.ts":
/*!*******************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/Media/index.ts ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_0__.Media; },\n/* harmony export */   checkArr: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_0__.checkArr; },\n/* harmony export */   formatDate: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_0__.formatDate; },\n/* harmony export */   formatExt: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_0__.formatExt; },\n/* harmony export */   useMediaHandlers: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_0__.useMediaHandlers; }\n/* harmony export */ });\n/* harmony import */ var _Media__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Media */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/Media.tsx\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0J1aWxkZXIvRmllbGRFZGl0b3IvcmVndWxhci9NZWRpYS9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvQnVpbGRlci9GaWVsZEVkaXRvci9yZWd1bGFyL01lZGlhL2luZGV4LnRzP2FkZTIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9NZWRpYSdcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/index.ts":
/*!*************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/index.ts ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Boolean: function() { return /* reexport safe */ _Boolean__WEBPACK_IMPORTED_MODULE_4__.Boolean; },\n/* harmony export */   ColorPicker: function() { return /* reexport safe */ _ColorPicker__WEBPACK_IMPORTED_MODULE_9__.ColorPicker; },\n/* harmony export */   Component: function() { return /* reexport safe */ _Component__WEBPACK_IMPORTED_MODULE_6__.Component; },\n/* harmony export */   DateTime: function() { return /* reexport safe */ _DateTime__WEBPACK_IMPORTED_MODULE_7__.DateTime; },\n/* harmony export */   LongText: function() { return /* reexport safe */ _LongText__WEBPACK_IMPORTED_MODULE_1__.LongText; },\n/* harmony export */   Media: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_5__.Media; },\n/* harmony export */   Relation: function() { return /* reexport safe */ _Relation__WEBPACK_IMPORTED_MODULE_3__.Relation; },\n/* harmony export */   RichText: function() { return /* reexport safe */ _RichText__WEBPACK_IMPORTED_MODULE_2__.RichText; },\n/* harmony export */   Selection: function() { return /* reexport safe */ _Selection__WEBPACK_IMPORTED_MODULE_8__.Selection; },\n/* harmony export */   Text: function() { return /* reexport safe */ _Text__WEBPACK_IMPORTED_MODULE_0__.Text; },\n/* harmony export */   checkArr: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_5__.checkArr; },\n/* harmony export */   formatDate: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_5__.formatDate; },\n/* harmony export */   formatExt: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_5__.formatExt; },\n/* harmony export */   useMediaHandlers: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_5__.useMediaHandlers; }\n/* harmony export */ });\n/* harmony import */ var _Text__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Text */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Text/index.ts\");\n/* harmony import */ var _LongText__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./LongText */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/LongText/index.ts\");\n/* harmony import */ var _RichText__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RichText */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/RichText/index.ts\");\n/* harmony import */ var _Relation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Relation */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Relation/index.ts\");\n/* harmony import */ var _Boolean__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Boolean */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Boolean/index.ts\");\n/* harmony import */ var _Media__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Media */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/index.ts\");\n/* harmony import */ var _Component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Component */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Component/index.ts\");\n/* harmony import */ var _DateTime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DateTime */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/DateTime/index.ts\");\n/* harmony import */ var _Selection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Selection */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Selection/index.ts\");\n/* harmony import */ var _ColorPicker__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ColorPicker */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/ColorPicker/index.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0J1aWxkZXIvRmllbGRFZGl0b3IvcmVndWxhci9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBc0I7QUFDSTtBQUNBO0FBQ0E7QUFDRDtBQUNGO0FBQ0k7QUFDRDtBQUNDO0FBQ0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvQnVpbGRlci9GaWVsZEVkaXRvci9yZWd1bGFyL2luZGV4LnRzPzc3MDgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9UZXh0J1xuZXhwb3J0ICogZnJvbSAnLi9Mb25nVGV4dCdcbmV4cG9ydCAqIGZyb20gJy4vUmljaFRleHQnXG5leHBvcnQgKiBmcm9tICcuL1JlbGF0aW9uJ1xuZXhwb3J0ICogZnJvbSAnLi9Cb29sZWFuJ1xuZXhwb3J0ICogZnJvbSAnLi9NZWRpYSdcbmV4cG9ydCAqIGZyb20gJy4vQ29tcG9uZW50J1xuZXhwb3J0ICogZnJvbSAnLi9EYXRlVGltZSdcbmV4cG9ydCAqIGZyb20gJy4vU2VsZWN0aW9uJ1xuZXhwb3J0ICogZnJvbSAnLi9Db2xvclBpY2tlcidcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/index.ts\n"));

/***/ })

});