import { CKEditor } from '@ckeditor/ckeditor5-react'
import { useIsomorphicLayoutEffect } from '@collective/core'
import {
	ClassicEditor,
	Essentials,
	Paragraph,
	Typing,
	Enter,
	Clipboard,
	Undo,
	SourceEditing,
	Bold,
	Italic,
	Underline,
	Strikethrough,
	Link,
	Code,
} from 'ckeditor5'
import { useState } from 'react'
import type { FieldProps } from '../../FieldEditor'
import 'ckeditor5/ckeditor5.css'
import styles from './richtext.module.scss'

export interface RichTextProps<T> extends FieldProps<T> {
	value?: T
	onChange: (props: { field: string; value: string }) => void
}

export const RichText = <T,>(props: RichTextProps<T>) => {
	const { type, value, onChange, name } = props
	const propsType = type ?? ''

	// Xử lý giá trị ban đầu một cách rõ ràng hơn
	let initialValue = ''
	if (value !== undefined && value !== null) {
		initialValue = propsType === 'json' ? JSON.stringify(value) : String(value)
	}

	const [propsValue, setPropsValue] = useState(initialValue)
	// Tạo config mà không có initialData
	const config = {
		licenseKey: 'GPL',
		plugins: [
			Essentials,
			Paragraph,
			Bold,
			Italic,
			Underline,
			Strikethrough,
			Link,
			Code,
			SourceEditing,
			Typing,
			Enter,
			Clipboard,
			Undo,
		],
		toolbar: [
			'undo',
			'redo',
			'|',
			'bold',
			'italic',
			'underline',
			'strikethrough',
			'|',
			'link',
			'code',
			'sourceEditing',
		],
		// Không thêm initialData vào config
	}

	// Đảm bảo rằng data không bao giờ là undefined hoặc null
	const editorData = propsValue || ''

	useIsomorphicLayoutEffect(() => {
		props.value !== propsValue && setPropsValue(props.value as unknown as string)
	}, [props.value])

	return (
		<div className={styles.wrapper}>
			<CKEditor
				editor={ClassicEditor}
				// data={editorData} // Sử dụng editorData đã được xử lý
				config={config}
				onChange={(_event, editor) => {
					const newData = editor.getData()
					setPropsValue(newData)
					onChange?.({ field: name as string, value: newData })
				}}
			/>
		</div>
	)
}
