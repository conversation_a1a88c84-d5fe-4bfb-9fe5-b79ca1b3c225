"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/Media.tsx":
/*!********************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/Media/Media.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: function() { return /* binding */ Media; },\n/* harmony export */   checkArr: function() { return /* binding */ checkArr; },\n/* harmony export */   formatDate: function() { return /* binding */ formatDate; },\n/* harmony export */   formatExt: function() { return /* binding */ formatExt; },\n/* harmony export */   useMediaHandlers: function() { return /* binding */ useMediaHandlers; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Image/ImageV2.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/../../node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./media.module.scss */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/media.module.scss\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_media_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _MediaInfoLayer__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./MediaInfoLayer */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/MediaInfoLayer.tsx\");\n\n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar formatDate = function(date) {\n    return dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"D/M/YYYY\");\n};\nvar formatExt = function(ext) {\n    return ext.replace(\".\", \"\");\n};\nvar checkArr = function(value) {\n    return Array.isArray(value);\n};\n// Custom hook for shared media logic\nvar useMediaHandlers = function(propsValue, setPropsValue, currentMediaIdx, setCurrentMediaIdx, multiple, onChange, field) {\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_4__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__.PageBuilderContext);\n    var setMediaInfoData = context.setMediaInfoData, setActiveMediaId = context.setActiveMediaId;\n    // Utility functions\n    var createFileInput = function() {\n        var multiple = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        var input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"image/*,video/*,audio/*,.pdf,.doc,.docx\";\n        input.multiple = multiple;\n        return input;\n    };\n    var validateFileSize = function(file) {\n        var maxSize = 20 * 1024 * 1024 // 20MB\n        ;\n        var fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);\n        if (file.size > maxSize) {\n            return {\n                isValid: false,\n                errorMessage: 'File \"'.concat(file.name, '\" (').concat(fileSizeMB, \"MB) exceeds the 20MB limit.\")\n            };\n        }\n        return {\n            isValid: true\n        };\n    };\n    var fileToMediaProps = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(file) {\n            var _file_name_split_pop, url, width, height, dimensions, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        url = URL.createObjectURL(file);\n                        if (!file.type.startsWith(\"image/\")) return [\n                            3,\n                            4\n                        ];\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            getImageDimensions(url)\n                        ];\n                    case 2:\n                        dimensions = _state.sent();\n                        width = dimensions.width;\n                        height = dimensions.height;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.warn(\"Failed to get image dimensions:\", error);\n                        // Default dimensions for images if we can't read them\n                        width = 800;\n                        height = 600;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2,\n                            {\n                                id: Date.now() + Math.random(),\n                                name: file.name,\n                                url: url,\n                                ext: \".\" + ((_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase()),\n                                mime: file.type,\n                                size: file.size,\n                                alternativeText: file.name,\n                                width: width,\n                                height: height\n                            }\n                        ];\n                }\n            });\n        });\n        return function fileToMediaProps(file) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var getImageDimensions = function(url) {\n        return new Promise(function(resolve, reject) {\n            var img = document.createElement(\"img\");\n            img.onload = function() {\n                resolve({\n                    width: img.naturalWidth,\n                    height: img.naturalHeight\n                });\n            };\n            img.onerror = reject;\n            img.src = url;\n        });\n    };\n    var updatePropsValue = function(newValue) {\n        setPropsValue(newValue);\n        if (onChange) {\n            onChange({\n                field: field || \"media\",\n                value: newValue\n            });\n        }\n    };\n    var handleNextMedia1 = function() {\n        if (checkArr(propsValue) && propsValue.length > 0) {\n            setCurrentMediaIdx(function(prevIdx) {\n                return prevIdx + 1 < propsValue.length ? prevIdx + 1 : 0;\n            });\n        }\n    };\n    var handlePrevMedia1 = function() {\n        if (checkArr(propsValue) && propsValue.length > 0) {\n            setCurrentMediaIdx(function(prevIdx) {\n                return prevIdx - 1 >= 0 ? prevIdx - 1 : propsValue.length - 1;\n            });\n        }\n    };\n    var handleAdd = function() {\n        var input = createFileInput(multiple);\n        input.onchange = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(e) {\n                var files, validFiles, invalidFiles, newMediaItems, currentArray, newValue;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            files = e.target.files;\n                            if (!files) return [\n                                2\n                            ];\n                            validFiles = [];\n                            invalidFiles = [];\n                            // Validate each file\n                            Array.from(files).forEach(function(file) {\n                                var validation = validateFileSize(file);\n                                if (validation.isValid) {\n                                    validFiles.push(file);\n                                } else {\n                                    invalidFiles.push(validation.errorMessage || \"File \".concat(file.name, \" is invalid\"));\n                                }\n                            });\n                            // Show error messages for invalid files\n                            if (invalidFiles.length > 0) {\n                                alert(\"The following files were skipped:\\n\\n\".concat(invalidFiles.join(\"\\n\")));\n                            }\n                            if (!(validFiles.length > 0)) return [\n                                3,\n                                2\n                            ];\n                            return [\n                                4,\n                                Promise.all(validFiles.map(fileToMediaProps))\n                            ];\n                        case 1:\n                            newMediaItems = _state.sent();\n                            if (multiple) {\n                                currentArray = checkArr(propsValue) ? propsValue : [];\n                                newValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(currentArray).concat((0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(newMediaItems));\n                                updatePropsValue(newValue);\n                            } else {\n                                updatePropsValue(newMediaItems[0]);\n                            }\n                            _state.label = 2;\n                        case 2:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(e) {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        input.click();\n    };\n    var handleReplace = function() {\n        var input = createFileInput(false);\n        input.onchange = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(e) {\n                var files, validation, newMedia, newArray;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            files = e.target.files;\n                            if (!files || !files[0]) return [\n                                2\n                            ];\n                            validation = validateFileSize(files[0]);\n                            if (!validation.isValid) {\n                                alert(validation.errorMessage || \"File exceeds the 20MB limit.\");\n                                return [\n                                    2\n                                ];\n                            }\n                            return [\n                                4,\n                                fileToMediaProps(files[0])\n                            ];\n                        case 1:\n                            newMedia = _state.sent();\n                            if (multiple && checkArr(propsValue)) {\n                                newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n                                newArray[currentMediaIdx] = newMedia;\n                                updatePropsValue(newArray);\n                            } else {\n                                updatePropsValue(newMedia);\n                            }\n                            // Update context\n                            setMediaInfoData(newMedia);\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(e) {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        input.click();\n    };\n    var handleDuplicate = function() {\n        var currentMedia = checkArr(propsValue) ? propsValue[currentMediaIdx] : propsValue;\n        if (!currentMedia) return;\n        var duplicatedMedia = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, currentMedia), {\n            id: Date.now() + Math.random(),\n            name: \"The copy of \".concat(currentMedia.name)\n        });\n        if (multiple && checkArr(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n            newArray.splice(currentMediaIdx + 1, 0, duplicatedMedia);\n            updatePropsValue(newArray);\n        } else {\n            // For single mode, replace current with duplicate\n            updatePropsValue(duplicatedMedia);\n        }\n    };\n    var handleRemove = function() {\n        if (multiple && checkArr(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n            newArray.splice(currentMediaIdx, 1);\n            // Reset editing if removing currently edited media\n            setActiveMediaId(null);\n            setMediaInfoData({\n                name: \"\",\n                url: \"\"\n            });\n            // Adjust current index if needed\n            if (currentMediaIdx >= newArray.length && newArray.length > 0) {\n                setCurrentMediaIdx(newArray.length - 1);\n            }\n            updatePropsValue(newArray);\n        } else {\n            // For single mode, clear the media\n            setActiveMediaId(null);\n            setMediaInfoData({\n                name: \"\",\n                url: \"\"\n            });\n            updatePropsValue(null);\n        }\n    };\n    var handleDownload = function() {\n        var currentMedia = checkArr(propsValue) ? propsValue[currentMediaIdx] : propsValue;\n        if (!(currentMedia === null || currentMedia === void 0 ? void 0 : currentMedia.url)) return;\n        var link = document.createElement(\"a\");\n        link.href = currentMedia.url;\n        link.download = currentMedia.name || \"download\";\n        link.target = \"_blank\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    };\n    var handleAction = function(key) {\n        switch(key){\n            case \"add\":\n                handleAdd();\n                break;\n            case \"replace\":\n                handleReplace();\n                break;\n            case \"duplicate\":\n                handleDuplicate();\n                break;\n            case \"remove\":\n                handleRemove();\n                break;\n            case \"download\":\n                handleDownload();\n                break;\n            default:\n                break;\n        }\n    };\n    return {\n        handleAdd: handleAdd,\n        handleReplace: handleReplace,\n        handleDuplicate: handleDuplicate,\n        handleRemove: handleRemove,\n        handleDownload: handleDownload,\n        handleAction: handleAction,\n        handleNextMedia: handleNextMedia1,\n        handlePrevMedia: handlePrevMedia1\n    };\n};\n_s(useMediaHandlers, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar Media = function(props) {\n    _s1();\n    var _ref = props !== null && props !== void 0 ? props : {}, value = _ref.value, onChange = _ref.onChange, multiple = _ref.multiple;\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_4__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__.PageBuilderContext);\n    var mediaInfoData = context.mediaInfoData, setMediaInfoData = context.setMediaInfoData, activeMediaId = context.activeMediaId, setActiveMediaId = context.setActiveMediaId;\n    var mediaId = (0,react__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false), 2), isEdit = _useState[0], setisEdit = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(multiple ? value : value), 2), propsValue = _useState1[0], setPropsValue = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(checkArr(propsValue) ? propsValue[0] : propsValue), 2), currentMedia = _useState2[0], setCurrentMedia = _useState2[1];\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_12__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0), 2), currentMediaIdx = _useState3[0], setCurrentMediaIdx = _useState3[1];\n    // Use shared media handlers\n    var mediaHandlers = useMediaHandlers(propsValue, setPropsValue, currentMediaIdx, setCurrentMediaIdx, multiple, onChange, props.field);\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        if (checkArr(propsValue)) {\n            setCurrentMedia(propsValue[currentMediaIdx]);\n            setMediaInfoData(propsValue[currentMediaIdx]);\n        } else {\n            setCurrentMedia(propsValue);\n            setMediaInfoData(propsValue);\n        }\n    }, [\n        currentMediaIdx,\n        propsValue\n    ]);\n    // useIsomorphicLayoutEffect(() => {\n    // \tif (isEdit && currentMedia) {\n    // \t\thandleEdit()\n    // \t}\n    // }, [currentMedia])\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        mediaInfoData && mediaInfoData.name === \"\" && setisEdit(false);\n    }, [\n        mediaInfoData\n    ]);\n    (0,_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect)(function() {\n        setisEdit(activeMediaId === mediaId);\n    }, [\n        activeMediaId,\n        mediaId\n    ]);\n    var mediaToolbar = [\n        {\n            name: \"Add\",\n            icon: \"add\",\n            action: \"add\",\n            visible: !multiple\n        },\n        {\n            name: \"Replace\",\n            icon: \"replace\",\n            action: \"replace\"\n        },\n        {\n            name: \"Duplicate\",\n            icon: \"duplicate\",\n            action: \"duplicate\",\n            visible: !multiple\n        },\n        {\n            name: \"Remove\",\n            icon: \"remove\",\n            action: \"remove\"\n        },\n        {\n            name: \"Download\",\n            icon: \"download\",\n            action: \"download\",\n            visible: !isEdit\n        }\n    ];\n    var filteredMediaToolbar = mediaToolbar.filter(function(tool) {\n        return !tool.visible;\n    });\n    var handleEdit = function() {\n        console.log(currentMedia);\n        setMediaInfoData(currentMedia);\n        setActiveMediaId(mediaId);\n    };\n    var handleBack = function() {\n        setActiveMediaId(null);\n        setMediaInfoData({\n            name: \"\",\n            url: \"\"\n        });\n    };\n    var handleAction = function(key) {\n        switch(key){\n            case \"add\":\n                handleAdd();\n                break;\n            case \"replace\":\n                handleReplace();\n                break;\n            case \"duplicate\":\n                handleDuplicate();\n                break;\n            case \"remove\":\n                handleRemove();\n                break;\n            case \"download\":\n                handleDownload();\n                break;\n            default:\n                break;\n        }\n    };\n    // Utility functions\n    var createFileInput = function() {\n        var multiple = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        var input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"image/*,video/*\";\n        input.multiple = multiple;\n        return input;\n    };\n    var validateFileSize = function(file) {\n        var maxSize = 20 * 1024 * 1024 // 20MB\n        ;\n        var fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);\n        if (file.size > maxSize) {\n            return {\n                isValid: false,\n                errorMessage: 'File \"'.concat(file.name, '\" (').concat(fileSizeMB, \"MB) exceeds the 20MB limit.\")\n            };\n        }\n        return {\n            isValid: true\n        };\n    };\n    var fileToMediaProps = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(file) {\n            var _file_name_split_pop, url, width, height, dimensions, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        url = URL.createObjectURL(file);\n                        if (!file.type.startsWith(\"image/\")) return [\n                            3,\n                            4\n                        ];\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            getImageDimensions(url)\n                        ];\n                    case 2:\n                        dimensions = _state.sent();\n                        width = dimensions.width;\n                        height = dimensions.height;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.warn(\"Failed to get image dimensions:\", error);\n                        // Default dimensions for images if we can't read them\n                        width = 800;\n                        height = 600;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2,\n                            {\n                                id: Date.now() + Math.random(),\n                                name: file.name,\n                                url: url,\n                                ext: \".\" + ((_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase()),\n                                mime: file.type,\n                                size: file.size,\n                                alternativeText: file.name,\n                                width: width,\n                                height: height\n                            }\n                        ];\n                }\n            });\n        });\n        return function fileToMediaProps(file) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var getImageDimensions = function(url) {\n        return new Promise(function(resolve, reject) {\n            var img = document.createElement(\"img\");\n            img.onload = function() {\n                resolve({\n                    width: img.naturalWidth,\n                    height: img.naturalHeight\n                });\n            };\n            img.onerror = reject;\n            img.src = url;\n        });\n    };\n    var updatePropsValue = function(newValue) {\n        setPropsValue(newValue);\n        onChange({\n            field: props.field || \"media\",\n            value: newValue\n        });\n    };\n    var handleAdd = function() {\n        var input = createFileInput(multiple);\n        input.onchange = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(e) {\n                var files, validFiles, invalidFiles, newMediaItems, currentArray, newValue;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            files = e.target.files;\n                            if (!files) return [\n                                2\n                            ];\n                            validFiles = [];\n                            invalidFiles = [];\n                            // Validate each file\n                            Array.from(files).forEach(function(file) {\n                                var validation = validateFileSize(file);\n                                if (validation.isValid) {\n                                    validFiles.push(file);\n                                } else {\n                                    invalidFiles.push(validation.errorMessage || \"File \".concat(file.name, \" is invalid\"));\n                                }\n                            });\n                            // Show error messages for invalid files\n                            if (invalidFiles.length > 0) {\n                                alert(\"The following files were skipped:\\n\\n\".concat(invalidFiles.join(\"\\n\")));\n                            }\n                            if (!(validFiles.length > 0)) return [\n                                3,\n                                2\n                            ];\n                            return [\n                                4,\n                                Promise.all(validFiles.map(fileToMediaProps))\n                            ];\n                        case 1:\n                            newMediaItems = _state.sent();\n                            if (multiple) {\n                                currentArray = checkArr(propsValue) ? propsValue : [];\n                                newValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(currentArray).concat((0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(newMediaItems));\n                                updatePropsValue(newValue);\n                            } else {\n                                updatePropsValue(newMediaItems[0]);\n                            }\n                            _state.label = 2;\n                        case 2:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(e) {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        input.click();\n    };\n    var handleReplace = function() {\n        var input = createFileInput(false);\n        input.onchange = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_7__._)(function(e) {\n                var files, validation, newMedia, newArray;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_8__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            files = e.target.files;\n                            if (!files || !files[0]) return [\n                                2\n                            ];\n                            validation = validateFileSize(files[0]);\n                            if (!validation.isValid) {\n                                alert(validation.errorMessage || \"File exceeds the 20MB limit.\");\n                                return [\n                                    2\n                                ];\n                            }\n                            return [\n                                4,\n                                fileToMediaProps(files[0])\n                            ];\n                        case 1:\n                            newMedia = _state.sent();\n                            if (multiple && checkArr(propsValue)) {\n                                newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n                                newArray[currentMediaIdx] = newMedia;\n                                updatePropsValue(newArray);\n                            } else {\n                                updatePropsValue(newMedia);\n                            }\n                            // Update context if currently editing this media\n                            if (isEdit) {\n                                setMediaInfoData(newMedia);\n                            }\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(e) {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        input.click();\n    };\n    var handleDuplicate = function() {\n        if (!currentMedia) return;\n        var duplicatedMedia = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, currentMedia), {\n            id: Date.now() + Math.random(),\n            name: \"The copy of \".concat(currentMedia.name)\n        });\n        if (multiple && checkArr(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n            newArray.splice(currentMediaIdx + 1, 0, duplicatedMedia);\n            updatePropsValue(newArray);\n        } else {\n            // For single mode, replace current with duplicate\n            updatePropsValue(duplicatedMedia);\n        }\n    };\n    var handleRemove = function() {\n        if (multiple && checkArr(propsValue)) {\n            var newArray = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(propsValue);\n            newArray.splice(currentMediaIdx, 1);\n            // Reset editing if removing currently edited media\n            if (isEdit && currentMedia === propsValue[currentMediaIdx]) {\n                setActiveMediaId(null);\n                setMediaInfoData({\n                    name: \"\",\n                    url: \"\"\n                });\n            }\n            // Adjust current index if needed\n            if (currentMediaIdx >= newArray.length && newArray.length > 0) {\n                setCurrentMediaIdx(newArray.length - 1);\n            }\n            updatePropsValue(newArray);\n        } else {\n            // For single mode, clear the media\n            setActiveMediaId(null);\n            setMediaInfoData({\n                name: \"\",\n                url: \"\"\n            });\n            updatePropsValue(null);\n        }\n    };\n    var handleDownload = function() {\n        if (!(currentMedia === null || currentMedia === void 0 ? void 0 : currentMedia.url)) return;\n        var link = document.createElement(\"a\");\n        link.href = currentMedia.url;\n        link.download = currentMedia.name || \"download\";\n        link.target = \"_blank\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    };\n    var isBuilderMode = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function() {\n        return pathname === null || pathname === void 0 ? void 0 : pathname.startsWith(\"/content-builder/\");\n    }, [\n        pathname\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().controller),\n                style: {\n                    \"--controller-cols\": isBuilderMode ? 12 : 8\n                },\n                children: [\n                    multiple && !isEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__btn),\n                                onClick: handlePrevMedia,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"chevron-left\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__index),\n                                children: \"\".concat(currentMediaIdx + 1, \"/\").concat(checkArr(propsValue) ? propsValue.length : 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__btn),\n                                onClick: handleNextMedia,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"chevron-right\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 616,\n                        columnNumber: 6\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().body), !isBuilderMode && isEdit ? multiple ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().detailed__multi) : (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().detailed) : \"\"),\n                        children: [\n                            currentMedia ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().item),\n                                style: {\n                                    \"--height\": isBuilderMode ? \"160px\" : \"280px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().tag),\n                                        children: formatExt((currentMedia === null || currentMedia === void 0 ? void 0 : currentMedia.ext) || \"\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().thumbnail),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Image, {\n                                            media: currentMedia,\n                                            alt: \"\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 8\n                                    }, _this),\n                                    !isEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().mask),\n                                        title: \"Edit this media\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                            onClick: function() {\n                                                return handleEdit();\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                                type: \"cms\",\n                                                variant: \"edit\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 11\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 10\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 9\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 635,\n                                columnNumber: 7\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().empty),\n                                style: {\n                                    \"--height\": isBuilderMode ? \"160px\" : \"280px\"\n                                },\n                                title: \"Browse file(s)\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                        type: \"cms\",\n                                        variant: \"image\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Drop your file(s) here or\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                                onClick: function() {\n                                                    return handleAction(\"add\");\n                                                },\n                                                children: \"browse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 9\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                        children: \"Max. File Size: 20MB\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 8\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 7\n                            }, _this),\n                            isEdit && checkArr(propsValue) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__nav),\n                                        onClick: handlePrevMedia,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: \"chevron-left\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 676,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__list),\n                                        children: propsValue.map(function(media, idx) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__thumb), idx === currentMediaIdx ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"),\n                                                onClick: function() {\n                                                    return setCurrentMediaIdx(idx);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Image, {\n                                                    media: media,\n                                                    alt: \"\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                    lineNumber: 688,\n                                                    columnNumber: 11\n                                                }, _this)\n                                            }, idx, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 10\n                                            }, _this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__nav),\n                                        onClick: handleNextMedia,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: \"chevron-right\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 8\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 674,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 5\n                    }, _this),\n                    !isBuilderMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__list),\n                                children: filteredMediaToolbar.map(function(tool, idx) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button),\n                                        onClick: function() {\n                                            return handleAction(tool.action);\n                                        },\n                                        title: tool.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: tool.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 10\n                                        }, _this)\n                                    }, idx, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 9\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__fixed),\n                                children: !isEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button), (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().text)),\n                                    title: \"Edit\",\n                                    onClick: handleEdit,\n                                    children: \"Edit\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 9\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button), (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().text)),\n                                    title: \"Back\",\n                                    onClick: handleBack,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                        type: \"cms\",\n                                        variant: \"back\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 10\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 9\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 700,\n                        columnNumber: 6\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                lineNumber: 607,\n                columnNumber: 4\n            }, _this),\n            isEdit && activeMediaId === mediaId && mediaInfoData && mediaInfoData.name !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MediaInfoLayer__WEBPACK_IMPORTED_MODULE_17__.MediaInfoLayer, {\n                multiple: multiple,\n                toolbar: filteredMediaToolbar,\n                mediaList: propsValue\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                lineNumber: 737,\n                columnNumber: 5\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n        lineNumber: 606,\n        columnNumber: 3\n    }, _this);\n};\n_s1(Media, \"fQLp4Fh0w8dEUZYuAsi2m9x62Ks=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        react__WEBPACK_IMPORTED_MODULE_4__.useId,\n        useMediaHandlers,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Button_Icon_Image_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_13__.useIsomorphicLayoutEffect\n    ];\n});\n_c = Media;\nvar _c;\n$RefreshReg$(_c, \"Media\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/Media.tsx\n"));

/***/ })

});