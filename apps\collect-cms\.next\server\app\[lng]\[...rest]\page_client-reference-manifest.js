globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[lng]/[...rest]/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/layouts/builder/content/ContentBuilderLayout.tsx":{"*":{"id":"(ssr)/./src/layouts/builder/content/ContentBuilderLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/layouts/builder/page/PageBuilderLayout.tsx":{"*":{"id":"(ssr)/./src/layouts/builder/page/PageBuilderLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/core/dist/components/Icon/icon.module.scss":{"*":{"id":"(ssr)/../../packages/core/dist/components/Icon/icon.module.scss","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[lng]/(dashboard)/clientLayout.tsx":{"*":{"id":"(ssr)/./src/app/[lng]/(dashboard)/clientLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/NavigationContext.tsx":{"*":{"id":"(ssr)/./src/contexts/NavigationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/i18n/src/client/index.tsx":{"*":{"id":"(ssr)/../../packages/i18n/src/client/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/i18n/src/hooks/useLanguageSwitch.tsx":{"*":{"id":"(ssr)/../../packages/i18n/src/hooks/useLanguageSwitch.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/i18n/src/link/client/index.tsx":{"*":{"id":"(ssr)/../../packages/i18n/src/link/client/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx":{"*":{"id":"(ssr)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/ui-lib/src/contexts/NavigationContext.tsx":{"*":{"id":"(ssr)/../../packages/ui-lib/src/contexts/NavigationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx":{"*":{"id":"(ssr)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(ssr)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/[lng]/layout","static/chunks/app/%5Blng%5D/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/[lng]/layout","static/chunks/app/%5Blng%5D/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\layouts\\builder\\content\\ContentBuilderLayout.tsx":{"id":"(app-pages-browser)/./src/layouts/builder/content/ContentBuilderLayout.tsx","name":"*","chunks":[],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\layouts\\builder\\page\\PageBuilderLayout.tsx":{"id":"(app-pages-browser)/./src/layouts/builder/page/PageBuilderLayout.tsx","name":"*","chunks":[],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\packages\\core\\dist\\components\\Icon\\icon.module.scss":{"id":"(app-pages-browser)/../../packages/core/dist/components/Icon/icon.module.scss","name":"*","chunks":[],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\app\\[lng]\\(dashboard)\\clientLayout.tsx":{"id":"(app-pages-browser)/./src/app/[lng]/(dashboard)/clientLayout.tsx","name":"*","chunks":["app/[lng]/(dashboard)/layout","static/chunks/app/%5Blng%5D/(dashboard)/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\[lng]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\[lng]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/[lng]/layout","static/chunks/app/%5Blng%5D/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\contexts\\NavigationContext.tsx":{"id":"(app-pages-browser)/./src/contexts/NavigationContext.tsx","name":"*","chunks":["app/[lng]/layout","static/chunks/app/%5Blng%5D/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\packages\\i18n\\src\\client\\index.tsx":{"id":"(app-pages-browser)/../../packages/i18n/src/client/index.tsx","name":"*","chunks":["app/[lng]/layout","static/chunks/app/%5Blng%5D/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\packages\\i18n\\src\\hooks\\useLanguageSwitch.tsx":{"id":"(app-pages-browser)/../../packages/i18n/src/hooks/useLanguageSwitch.tsx","name":"*","chunks":["app/[lng]/layout","static/chunks/app/%5Blng%5D/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\packages\\i18n\\src\\link\\client\\index.tsx":{"id":"(app-pages-browser)/../../packages/i18n/src/link/client/index.tsx","name":"*","chunks":["app/[lng]/layout","static/chunks/app/%5Blng%5D/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\styles\\globals.scss":{"id":"(app-pages-browser)/./src/styles/globals.scss","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\packages\\ui-lib\\src\\contexts\\GeneralSettingContext.tsx":{"id":"(app-pages-browser)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\packages\\ui-lib\\src\\contexts\\NavigationContext.tsx":{"id":"(app-pages-browser)/../../packages/ui-lib/src/contexts/NavigationContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\packages\\ui-lib\\src\\contexts\\QuickNavigationContext.tsx":{"id":"(app-pages-browser)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\packages\\ui-lib\\src\\styles\\global.scss":{"id":"(app-pages-browser)/../../packages/ui-lib/src/styles/global.scss","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\app\\not-found.tsx":{"id":"(app-pages-browser)/./src/app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\":[],"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\app\\layout":["static/css/app/layout.css"],"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\app\\not-found":[],"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\app\\[lng]\\(dashboard)\\layout":["static/css/app/[lng]/(dashboard)/layout.css"],"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\app\\[lng]\\layout":["static/css/app/[lng]/layout.css"],"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\app\\[lng]\\not-found":[],"D:\\CDA\\repos\\brand-compass-frontend-template\\apps\\collect-cms\\src\\app\\[lng]\\[...rest]\\page":[]}}